# Testing Structure

Folder này chứa tất cả các file test và tài liệu liên quan đến testing cho dự án NS Shop.

## 📁 Cấu trúc Folder

```
tests/
├── unit/                    # Unit tests
│   ├── components/         # Component tests
│   │   ├── ui/            # UI component tests
│   │   ├── admin/         # Admin component tests
│   │   ├── auth/          # Authentication component tests
│   │   └── layout/        # Layout component tests
│   ├── hooks/             # Custom hook tests
│   ├── lib/               # Utility function tests
│   ├── api/               # API route unit tests
│   └── helpers/           # Helper function tests
├── integration/            # Integration tests
│   ├── api/               # API integration tests
│   └── database/          # Database integration tests
├── manual/                # Manual test scripts
│   ├── auth-integration.js        # Authentication manual test
│   ├── signin-integration.js      # Sign-in manual test
│   └── profile-update-integration.js # Profile update manual test
├── mocks/                 # Mock definitions
│   ├── handlers.ts        # MSW handlers
│   └── server.ts          # MSW server setup
├── helpers/               # Test utilities
│   ├── test-utils.tsx     # React Testing Library setup
│   ├── database.ts        # Database test helpers
│   ├── profile-helpers.ts # Profile test helpers
│   └── search-helpers.ts  # Search test helpers
├── fixtures/              # Mock data
│   └── mock-data.ts       # Test data fixtures
├── docs/                  # Test documentation
│   └── test-features.md   # Feature testing documentation
├── __mocks__/             # Jest mocks
│   ├── api-handlers.ts    # API mock handlers
│   └── server.ts          # Mock server
├── setup.ts               # Jest setup file
├── polyfills.ts           # Test polyfills
├── tsconfig.json          # TypeScript config for tests
└── README.md              # This file
```

## 🧪 Loại Tests

### Unit Tests (`tests/unit/`)
- Test các component riêng lẻ
- Test các function utility
- Test các custom hooks
- Test các API routes (logic only)

### Integration Tests (`tests/integration/`)
- Test tương tác giữa các components
- Test API endpoints với database
- Test complete user flows

### Manual Tests (`tests/manual/`)
- Scripts để test thủ công với database thực
- Verification scripts cho các features
- Debug và troubleshooting tools

## 🚀 Chạy Tests

### Commands Cơ Bản
```bash
# Chạy tất cả tests
npm test

# Chạy tests với coverage
npm run test:coverage

# Chạy tests ở watch mode
npm run test:watch

# Chạy unit tests only
npm run test:unit

# Chạy integration tests only
npm run test:integration
```

### Chạy Manual Tests
```bash
# Authentication test
node tests/manual/auth-integration.js

# Sign-in test
node tests/manual/signin-integration.js

# Profile update test
node tests/manual/profile-update-integration.js
```

## 📋 Test Patterns

### Unit Test Example
```typescript
// tests/unit/components/button.test.tsx
import { render, screen } from '../../helpers/test-utils';
import { Button } from '@/components/ui/button';

describe('Button Component', () => {
  it('should render correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

### Integration Test Example
```typescript
// tests/integration/api/auth.test.ts
import { POST } from '@/app/api/auth/register/route';
import { prisma } from '@/lib/prisma';

describe('Auth API Integration', () => {
  it('should register new user', async () => {
    // Test implementation
  });
});
```

## 🔧 Configuration

- **Jest Config**: `jest.config.js` (root level)
- **TypeScript Config**: `tests/tsconfig.json`
- **Test Setup**: `tests/setup.ts`
- **Polyfills**: `tests/polyfills.ts`

## 📊 Coverage

Coverage reports được generate vào folder `coverage/`:
- HTML report: `coverage/lcov-report/index.html`
- Text report: Terminal output
- LCOV format: `coverage/lcov.info`

## 🎯 Best Practices

1. **Tên file**: Sử dụng `.test.ts/tsx` cho unit tests, `.integration.test.ts/tsx` cho integration tests
2. **Mocking**: Sử dụng MSW cho API mocking, Jest mocks cho modules
3. **Test Data**: Sử dụng fixtures trong `tests/fixtures/`
4. **Helpers**: Tái sử dụng test utilities trong `tests/helpers/`
5. **Cleanup**: Luôn cleanup sau mỗi test (database, mocks, etc.)

## 📚 Documentation

- [Testing Guide](docs/testing/TESTING_GUIDE.md) - Hướng dẫn chi tiết về testing
- [Test Features](docs/test-features.md) - Documentation về các features đã test
