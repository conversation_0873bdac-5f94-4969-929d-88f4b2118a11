import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CreateAdminDialog } from '@/components/admin/create-admin-dialog';
import { EditAdminDialog } from '@/components/admin/edit-admin-dialog';

// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

const { useSession } = require('next-auth/react');

describe('Admin Dialog Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    global.fetch = jest.fn();
  });

  describe('CreateAdminDialog', () => {
    const defaultProps = {
      open: true,
      onOpenChange: jest.fn(),
      onSuccess: jest.fn(),
    };

    it('should render create admin form', () => {
      render(<CreateAdminDialog {...defaultProps} />);

      expect(screen.getByText('Tạo tài khoản Admin')).toBeInTheDocument();
      expect(screen.getByLabelText('Họ tên *')).toBeInTheDocument();
      expect(screen.getByLabelText('Email *')).toBeInTheDocument();
      expect(screen.getByLabelText('Mật khẩu *')).toBeInTheDocument();
      expect(screen.getByLabelText('Xác nhận mật khẩu *')).toBeInTheDocument();
      expect(screen.getByLabelText('Vai trò *')).toBeInTheDocument();
      expect(screen.getByLabelText('Số điện thoại')).toBeInTheDocument();
      expect(screen.getByLabelText('Phòng ban')).toBeInTheDocument();
    });

    it('should validate password confirmation', async () => {
      render(<CreateAdminDialog {...defaultProps} />);

      const nameInput = screen.getByLabelText('Họ tên *');
      const emailInput = screen.getByLabelText('Email *');
      const passwordInput = screen.getByLabelText('Mật khẩu *');
      const confirmPasswordInput = screen.getByLabelText('Xác nhận mật khẩu *');
      const submitButton = screen.getByRole('button', { name: 'Tạo tài khoản' });

      fireEvent.change(nameInput, { target: { value: 'Test Admin' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'different123' } });

      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Mật khẩu xác nhận không khớp')).toBeInTheDocument();
      });
    });

    it('should validate password length', async () => {
      render(<CreateAdminDialog {...defaultProps} />);

      const nameInput = screen.getByLabelText('Họ tên *');
      const emailInput = screen.getByLabelText('Email *');
      const passwordInput = screen.getByLabelText('Mật khẩu *');
      const confirmPasswordInput = screen.getByLabelText('Xác nhận mật khẩu *');
      const submitButton = screen.getByRole('button', { name: 'Tạo tài khoản' });

      fireEvent.change(nameInput, { target: { value: 'Test Admin' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: '123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: '123' } });

      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Mật khẩu phải có ít nhất 6 ký tự')).toBeInTheDocument();
      });
    });

    it('should submit form with valid data', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          message: 'Tạo tài khoản quản trị viên thành công',
          adminUser: { id: '1', name: 'Test Admin', email: '<EMAIL>' },
        }),
      });

      render(<CreateAdminDialog {...defaultProps} />);

      const nameInput = screen.getByLabelText('Họ tên *');
      const emailInput = screen.getByLabelText('Email *');
      const passwordInput = screen.getByLabelText('Mật khẩu *');
      const confirmPasswordInput = screen.getByLabelText('Xác nhận mật khẩu *');
      const phoneInput = screen.getByLabelText('Số điện thoại');
      const departmentInput = screen.getByLabelText('Phòng ban');
      const submitButton = screen.getByRole('button', { name: 'Tạo tài khoản' });

      fireEvent.change(nameInput, { target: { value: 'Test Admin' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } });
      fireEvent.change(phoneInput, { target: { value: '123456789' } });
      fireEvent.change(departmentInput, { target: { value: 'IT' } });

      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/admin/admins', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Test Admin',
            email: '<EMAIL>',
            password: 'password123',
            role: 'MODERATOR',
            phone: '123456789',
            department: 'IT',
            permissions: {
              manage_products: true,
              manage_orders: true,
              manage_categories: true,
              view_analytics: true,
              manage_users: true,
            },
          }),
        });
      });

      expect(defaultProps.onSuccess).toHaveBeenCalled();
      expect(defaultProps.onOpenChange).toHaveBeenCalledWith(false);
    });

    it('should handle API errors', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        json: () => Promise.resolve({
          error: 'Email đã được sử dụng',
        }),
      });

      render(<CreateAdminDialog {...defaultProps} />);

      const nameInput = screen.getByLabelText('Họ tên *');
      const emailInput = screen.getByLabelText('Email *');
      const passwordInput = screen.getByLabelText('Mật khẩu *');
      const confirmPasswordInput = screen.getByLabelText('Xác nhận mật khẩu *');
      const submitButton = screen.getByRole('button', { name: 'Tạo tài khoản' });

      fireEvent.change(nameInput, { target: { value: 'Test Admin' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } });

      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Email đã được sử dụng')).toBeInTheDocument();
      });
    });
  });

  describe('EditAdminDialog', () => {
    const mockAdmin = {
      id: '1',
      name: 'Test Admin',
      email: '<EMAIL>',
      phone: '123456789',
      role: 'MODERATOR' as const,
      isActive: true,
      department: 'IT',
    };

    const defaultProps = {
      admin: mockAdmin,
      open: true,
      onOpenChange: jest.fn(),
      onSuccess: jest.fn(),
    };

    beforeEach(() => {
      useSession.mockReturnValue({
        data: {
          user: { id: '2', role: 'ADMIN' },
        },
      });
    });

    it('should render edit admin form with existing data', () => {
      render(<EditAdminDialog {...defaultProps} />);

      expect(screen.getByText('Chỉnh sửa tài khoản Admin')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Admin')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('123456789')).toBeInTheDocument();
      expect(screen.getByDisplayValue('IT')).toBeInTheDocument();
    });

    it('should allow admin to change role and status', () => {
      render(<EditAdminDialog {...defaultProps} />);

      const roleSelect = screen.getByRole('combobox');
      const activeSwitch = screen.getByRole('switch');

      expect(roleSelect).not.toBeDisabled();
      expect(activeSwitch).toBeInTheDocument();
    });

    it('should prevent non-admin from changing role', () => {
      useSession.mockReturnValue({
        data: {
          user: { id: '2', role: 'MODERATOR' },
        },
      });

      render(<EditAdminDialog {...defaultProps} />);

      const roleSelect = screen.getByRole('combobox');
      expect(roleSelect).toBeDisabled();
      expect(screen.getByText('Chỉ Admin mới có thể thay đổi vai trò')).toBeInTheDocument();
    });

    it('should prevent user from changing their own role', () => {
      useSession.mockReturnValue({
        data: {
          user: { id: '1', role: 'ADMIN' }, // Same ID as the admin being edited
        },
      });

      render(<EditAdminDialog {...defaultProps} />);

      const roleSelect = screen.getByRole('combobox');
      expect(roleSelect).toBeDisabled();
      expect(screen.getByText('Không thể thay đổi vai trò của chính mình')).toBeInTheDocument();
    });

    it('should submit form with updated data', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          message: 'Cập nhật thông tin thành công',
          adminUser: { ...mockAdmin, name: 'Updated Admin' },
        }),
      });

      render(<EditAdminDialog {...defaultProps} />);

      const nameInput = screen.getByDisplayValue('Test Admin');
      const submitButton = screen.getByRole('button', { name: 'Cập nhật' });

      fireEvent.change(nameInput, { target: { value: 'Updated Admin' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/admin/admins/1', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Updated Admin',
            email: '<EMAIL>',
            phone: '123456789',
            department: 'IT',
            role: 'MODERATOR',
            isActive: true,
          }),
        });
      });

      expect(defaultProps.onSuccess).toHaveBeenCalled();
      expect(defaultProps.onOpenChange).toHaveBeenCalledWith(false);
    });

    it('should handle password updates', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          message: 'Cập nhật thông tin thành công',
        }),
      });

      render(<EditAdminDialog {...defaultProps} />);

      const passwordInput = screen.getByLabelText('Mật khẩu mới');
      const confirmPasswordInput = screen.getByLabelText('Xác nhận mật khẩu');
      const submitButton = screen.getByRole('button', { name: 'Cập nhật' });

      fireEvent.change(passwordInput, { target: { value: 'newpassword123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'newpassword123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/admin/admins/1', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Test Admin',
            email: '<EMAIL>',
            phone: '123456789',
            department: 'IT',
            password: 'newpassword123',
            role: 'MODERATOR',
            isActive: true,
          }),
        });
      });
    });

    it('should validate password confirmation on update', async () => {
      render(<EditAdminDialog {...defaultProps} />);

      const passwordInput = screen.getByLabelText('Mật khẩu mới');
      const confirmPasswordInput = screen.getByLabelText('Xác nhận mật khẩu');
      const submitButton = screen.getByRole('button', { name: 'Cập nhật' });

      fireEvent.change(passwordInput, { target: { value: 'newpassword123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'different123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Mật khẩu xác nhận không khớp')).toBeInTheDocument();
      });
    });
  });
});
