import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

describe('AdminUser Model', () => {
  beforeEach(async () => {
    // Clean up admin users before each test
    await prisma.adminUser.deleteMany();
  });

  afterEach(async () => {
    // Clean up after each test
    await prisma.adminUser.deleteMany();
  });

  describe('Creation', () => {
    it('should create an admin user with required fields', async () => {
      const hashedPassword = await bcrypt.hash('password123', 12);
      
      const adminUser = await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'Test Admin',
          password: hashedPassword,
          role: 'ADMIN',
        },
      });

      expect(adminUser).toBeDefined();
      expect(adminUser.email).toBe('<EMAIL>');
      expect(adminUser.name).toBe('Test Admin');
      expect(adminUser.role).toBe('ADMIN');
      expect(adminUser.isActive).toBe(true); // Default value
      expect(adminUser.id).toBeDefined();
      expect(adminUser.createdAt).toBeDefined();
      expect(adminUser.updatedAt).toBeDefined();
    });

    it('should create a moderator user with permissions', async () => {
      const hashedPassword = await bcrypt.hash('password123', 12);
      const permissions = {
        manage_products: true,
        manage_orders: true,
        manage_categories: false,
        view_analytics: true,
        manage_users: false,
      };

      const moderator = await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'Test Moderator',
          password: hashedPassword,
          role: 'MODERATOR',
          permissions,
          department: 'IT',
        },
      });

      expect(moderator.role).toBe('MODERATOR');
      expect(moderator.permissions).toEqual(permissions);
      expect(moderator.department).toBe('IT');
    });

    it('should enforce unique email constraint', async () => {
      const hashedPassword = await bcrypt.hash('password123', 12);
      
      await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'First Admin',
          password: hashedPassword,
          role: 'ADMIN',
        },
      });

      await expect(
        prisma.adminUser.create({
          data: {
            email: '<EMAIL>',
            name: 'Second Admin',
            password: hashedPassword,
            role: 'ADMIN',
          },
        })
      ).rejects.toThrow();
    });

    it('should set default role to MODERATOR', async () => {
      const hashedPassword = await bcrypt.hash('password123', 12);
      
      const adminUser = await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'Default User',
          password: hashedPassword,
          // No role specified, should default to MODERATOR
        },
      });

      expect(adminUser.role).toBe('MODERATOR');
    });
  });

  describe('Relationships', () => {
    it('should support admin hierarchy (createdBy relationship)', async () => {
      const hashedPassword = await bcrypt.hash('password123', 12);
      
      // Create parent admin
      const parentAdmin = await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'Parent Admin',
          password: hashedPassword,
          role: 'ADMIN',
        },
      });

      // Create child admin
      const childAdmin = await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'Child Admin',
          password: hashedPassword,
          role: 'MODERATOR',
          createdBy: parentAdmin.id,
        },
      });

      // Fetch with relationship
      const adminWithCreator = await prisma.adminUser.findUnique({
        where: { id: childAdmin.id },
        include: {
          createdByAdmin: true,
        },
      });

      expect(adminWithCreator?.createdByAdmin?.id).toBe(parentAdmin.id);
      expect(adminWithCreator?.createdByAdmin?.name).toBe('Parent Admin');
    });

    it('should support finding created admins', async () => {
      const hashedPassword = await bcrypt.hash('password123', 12);
      
      // Create parent admin
      const parentAdmin = await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'Parent Admin',
          password: hashedPassword,
          role: 'ADMIN',
        },
      });

      // Create multiple child admins
      await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'Child Admin 1',
          password: hashedPassword,
          role: 'MODERATOR',
          createdBy: parentAdmin.id,
        },
      });

      await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'Child Admin 2',
          password: hashedPassword,
          role: 'MODERATOR',
          createdBy: parentAdmin.id,
        },
      });

      // Fetch parent with created admins
      const parentWithChildren = await prisma.adminUser.findUnique({
        where: { id: parentAdmin.id },
        include: {
          createdAdmins: true,
        },
      });

      expect(parentWithChildren?.createdAdmins).toHaveLength(2);
      expect(parentWithChildren?.createdAdmins.map(a => a.email)).toContain('<EMAIL>');
      expect(parentWithChildren?.createdAdmins.map(a => a.email)).toContain('<EMAIL>');
    });
  });

  describe('Updates', () => {
    it('should update admin user fields', async () => {
      const hashedPassword = await bcrypt.hash('password123', 12);
      
      const adminUser = await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'Original Name',
          password: hashedPassword,
          role: 'MODERATOR',
          isActive: true,
        },
      });

      const updatedAdmin = await prisma.adminUser.update({
        where: { id: adminUser.id },
        data: {
          name: 'Updated Name',
          phone: '123456789',
          department: 'Marketing',
          isActive: false,
        },
      });

      expect(updatedAdmin.name).toBe('Updated Name');
      expect(updatedAdmin.phone).toBe('123456789');
      expect(updatedAdmin.department).toBe('Marketing');
      expect(updatedAdmin.isActive).toBe(false);
    });

    it('should update lastLoginAt timestamp', async () => {
      const hashedPassword = await bcrypt.hash('password123', 12);
      
      const adminUser = await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'Test Admin',
          password: hashedPassword,
          role: 'ADMIN',
        },
      });

      expect(adminUser.lastLoginAt).toBeNull();

      const loginTime = new Date();
      const updatedAdmin = await prisma.adminUser.update({
        where: { id: adminUser.id },
        data: {
          lastLoginAt: loginTime,
        },
      });

      expect(updatedAdmin.lastLoginAt).toEqual(loginTime);
    });
  });

  describe('Queries', () => {
    beforeEach(async () => {
      const hashedPassword = await bcrypt.hash('password123', 12);
      
      // Create test data
      await prisma.adminUser.createMany({
        data: [
          {
            email: '<EMAIL>',
            name: 'Admin One',
            password: hashedPassword,
            role: 'ADMIN',
            department: 'IT',
            isActive: true,
          },
          {
            email: '<EMAIL>',
            name: 'Admin Two',
            password: hashedPassword,
            role: 'ADMIN',
            department: 'Marketing',
            isActive: false,
          },
          {
            email: '<EMAIL>',
            name: 'Moderator One',
            password: hashedPassword,
            role: 'MODERATOR',
            department: 'IT',
            isActive: true,
          },
        ],
      });
    });

    it('should filter by role', async () => {
      const admins = await prisma.adminUser.findMany({
        where: { role: 'ADMIN' },
      });

      const moderators = await prisma.adminUser.findMany({
        where: { role: 'MODERATOR' },
      });

      expect(admins).toHaveLength(2);
      expect(moderators).toHaveLength(1);
    });

    it('should filter by active status', async () => {
      const activeUsers = await prisma.adminUser.findMany({
        where: { isActive: true },
      });

      const inactiveUsers = await prisma.adminUser.findMany({
        where: { isActive: false },
      });

      expect(activeUsers).toHaveLength(2);
      expect(inactiveUsers).toHaveLength(1);
    });

    it('should search by name and email', async () => {
      const searchResults = await prisma.adminUser.findMany({
        where: {
          OR: [
            { name: { contains: 'Admin', mode: 'insensitive' } },
            { email: { contains: 'admin', mode: 'insensitive' } },
          ],
        },
      });

      expect(searchResults).toHaveLength(2);
    });

    it('should filter by department', async () => {
      const itUsers = await prisma.adminUser.findMany({
        where: { department: 'IT' },
      });

      expect(itUsers).toHaveLength(2);
    });
  });
});
