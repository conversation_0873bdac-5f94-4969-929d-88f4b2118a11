import React, { ReactElement } from "react";
import { render, RenderOptions } from "@testing-library/react";
import { ThemeProvider } from "next-themes";

// Mock providers for testing
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider attribute="class" defaultTheme="light">
      {children}
    </ThemeProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, "wrapper">
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from "@testing-library/react";
export { customRender as render };

// Common test utilities
export const createMockUser = (overrides = {}) => ({
  id: "test-user-id",
  email: "<EMAIL>",
  name: "Test User",
  role: "USER",
  avatar: null,
  phone: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

export const createMockProduct = (overrides = {}) => ({
  id: "test-product-id",
  name: "Test Product",
  description: "Test product description",
  price: 99.99,
  salePrice: null,
  images: ["https://example.com/image.jpg"],
  categoryId: "test-category-id",
  stock: 10,
  sku: "TEST-SKU-001",
  slug: "test-product",
  featured: false,
  status: "ACTIVE",
  tags: ["test"],
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

export const createMockCategory = (overrides = {}) => ({
  id: "test-category-id",
  name: "Test Category",
  description: "Test category description",
  slug: "test-category",
  image: "https://example.com/category.jpg",
  parentId: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

export const createMockOrder = (overrides = {}) => ({
  id: "test-order-id",
  userId: "test-user-id",
  total: 199.98,
  status: "PENDING",
  paymentMethod: "COD",
  paymentStatus: "PENDING",
  shippingAddress: {
    fullName: "Test User",
    phone: "0123456789",
    address: "123 Test Street",
    ward: "Test Ward",
    district: "Test District",
    province: "Test Province",
  },
  billingAddress: null,
  notes: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

export const createMockCartItem = (overrides = {}) => ({
  id: "test-cart-item-id",
  cartId: "test-cart-id",
  productId: "test-product-id",
  quantity: 2,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

// Mock API responses
export const mockApiResponse = (data: any, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  json: async () => data,
  text: async () => JSON.stringify(data),
});

// Mock fetch function
export const mockFetch = (response: any, status = 200) => {
  global.fetch = jest.fn(() =>
    Promise.resolve(mockApiResponse(response, status))
  ) as jest.Mock;
};

// Wait for async operations (renamed to avoid conflict with RTL waitFor)
export const waitForTimeout = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

// Mock localStorage
export const mockLocalStorage = () => {
  const store: { [key: string]: string } = {};

  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach((key) => delete store[key]);
    }),
  };
};

// Mock sessionStorage
export const mockSessionStorage = () => {
  const store: { [key: string]: string } = {};

  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach((key) => delete store[key]);
    }),
  };
};

// Form testing utilities
export const fillForm = async (
  form: HTMLFormElement,
  data: Record<string, string>
) => {
  const { fireEvent } = await import("@testing-library/react");

  Object.entries(data).forEach(([name, value]) => {
    const input = form.querySelector(`[name="${name}"]`) as HTMLInputElement;
    if (input) {
      fireEvent.change(input, { target: { value } });
    }
  });
};

// Error boundary for testing
export class TestErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Test Error Boundary caught an error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div data-testid="error-boundary">Something went wrong.</div>;
    }

    return this.props.children;
  }
}
