// Jest setup file
import "@testing-library/jest-dom";

// Polyfill fetch for Node.js environment
import "whatwg-fetch";

// Setup MSW server (temporarily disabled due to Node.js compatibility issues)
// import "./mocks/server";

// Mock Prisma Client globally
jest.mock("@/lib/prisma", () => {
  const { mockPrisma } = require("./__mocks__/prisma");
  return { prisma: mockPrisma };
});

// Mock bcrypt globally
jest.mock("bcryptjs", () => {
  const { mockBcrypt } = require("./__mocks__/prisma");
  return mockBcrypt;
});

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return "/";
  },
}));

// Mock Next.js image component
jest.mock("next/image", () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const React = require("react");
    return React.createElement("img", props);
  },
}));

// Mock Framer Motion
jest.mock("framer-motion", () => {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const React = require("react");
  return {
    motion: {
      div: ({ children, ...props }: any) =>
        React.createElement("div", props, children),
      span: ({ children, ...props }: any) =>
        React.createElement("span", props, children),
      button: ({ children, ...props }: any) =>
        React.createElement("button", props, children),
      form: ({ children, ...props }: any) =>
        React.createElement("form", props, children),
      section: ({ children, ...props }: any) =>
        React.createElement("section", props, children),
      article: ({ children, ...props }: any) =>
        React.createElement("article", props, children),
      header: ({ children, ...props }: any) =>
        React.createElement("header", props, children),
      nav: ({ children, ...props }: any) =>
        React.createElement("nav", props, children),
      main: ({ children, ...props }: any) =>
        React.createElement("main", props, children),
      aside: ({ children, ...props }: any) =>
        React.createElement("aside", props, children),
      footer: ({ children, ...props }: any) =>
        React.createElement("footer", props, children),
    },
    AnimatePresence: ({ children }: any) => children,
    useAnimation: () => ({
      start: jest.fn(),
      stop: jest.fn(),
      set: jest.fn(),
    }),
    useInView: () => true,
  };
});

// Mock environment variables
Object.defineProperty(process.env, "NODE_ENV", {
  value: "test",
  writable: true,
});
Object.defineProperty(process.env, "NEXTAUTH_SECRET", {
  value: "test-secret",
  writable: true,
});
Object.defineProperty(process.env, "NEXTAUTH_URL", {
  value: "http://localhost:3000",
  writable: true,
});
Object.defineProperty(process.env, "DATABASE_URL", {
  value: "postgresql://test:test@localhost:5499/ns_shop_test",
  writable: true,
});

// Global test utilities
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, "scrollTo", {
  writable: true,
  value: jest.fn(),
});

// Suppress console errors in tests unless explicitly needed
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === "string" &&
      args[0].includes("Warning: ReactDOM.render is no longer supported")
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});
