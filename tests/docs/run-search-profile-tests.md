# Hướng dẫn chạy Tests cho Search Product và Update Profile

## 📋 Tổng quan

Đã triển khai đầy đủ testing cho hai tính năng chính:
1. **Search Product** - Tìm kiếm sản phẩm
2. **Update Profile** - C<PERSON><PERSON> nhật thông tin cá nhân

## 🧪 Cấu trúc Tests

### Unit Tests

#### Search Product
- `tests/unit/components/search/search-input.test.tsx` - Test search input component
- `tests/unit/api/search.test.ts` - Test search API endpoints
- `tests/unit/helpers/search-helpers.test.ts` - Test search helper functions

#### Update Profile
- `tests/unit/components/profile/profile-form.test.tsx` - Test profile form component
- `tests/unit/api/profile.test.ts` - Test profile API endpoints
- `tests/unit/helpers/profile-helpers.test.ts` - Test profile helper functions

### Integration Tests
- `tests/integration/search-flow.test.tsx` - Test toàn bộ flow search
- `tests/integration/profile-flow.test.tsx` - Test toàn bộ flow update profile

### Test Helpers và Mock Data
- `tests/helpers/search-helpers.ts` - Helper functions cho search testing
- `tests/helpers/profile-helpers.ts` - Helper functions cho profile testing
- `tests/fixtures/mock-data.ts` - Mock data đã được mở rộng

## 🚀 Cách chạy Tests

### Chạy tất cả tests
```bash
npm test
```

### Chạy tests cho Search Product
```bash
# Unit tests
npm test -- tests/unit/components/search/
npm test -- tests/unit/api/search.test.ts
npm test -- tests/unit/helpers/search-helpers.test.ts

# Integration tests
npm test -- tests/integration/search-flow.test.tsx
```

### Chạy tests cho Update Profile
```bash
# Unit tests
npm test -- tests/unit/components/profile/
npm test -- tests/unit/api/profile.test.ts
npm test -- tests/unit/helpers/profile-helpers.test.ts

# Integration tests
npm test -- tests/integration/profile-flow.test.tsx
```

### Chạy tests với coverage
```bash
npm test -- --coverage
```

### Chạy tests ở watch mode
```bash
npm test -- --watch
```

## 📊 Test Coverage

### Search Product Tests
- ✅ **Search Input Component** (Desktop & Mobile)
  - Form submission và navigation
  - Input validation và encoding
  - Mobile menu interaction
  - Accessibility testing

- ✅ **Search API**
  - Query filtering (name, description, tags)
  - Category filtering
  - Price range filtering
  - Pagination và sorting
  - Error handling

- ✅ **Search Helpers**
  - Filter functions
  - URL building và parsing
  - Validation functions
  - Performance measurement

- ✅ **Search Flow Integration**
  - Complete search flow từ input đến results
  - Advanced search features
  - Error handling và loading states
  - Performance testing

### Update Profile Tests
- ✅ **Profile Form Component**
  - Authentication checks
  - Form display và editing
  - Form validation
  - Update submission
  - Loading states

- ✅ **Profile API**
  - GET profile endpoint
  - PUT profile endpoint
  - Authentication validation
  - Data validation
  - Error handling

- ✅ **Profile Helpers**
  - Data validation functions
  - Date formatting
  - Gender formatting
  - Mock data generation
  - Update flow testing

- ✅ **Profile Flow Integration**
  - Complete update flow từ form đến API
  - Form validation flow
  - Cancel và reset functionality
  - Authentication flow
  - Loading states

## 🔧 Test Configuration

### Jest Configuration
Tests sử dụng Jest với các setup sau:
- React Testing Library
- MSW (Mock Service Worker) cho API mocking
- Custom test utilities
- Mock data fixtures

### Mock Setup
- NextAuth session mocking
- Next.js router mocking
- API endpoints mocking với MSW
- Toast notifications mocking

## 📝 Test Scenarios

### Search Product Scenarios
1. **Basic Search**
   - Search từ header (desktop/mobile)
   - Navigation đến search page
   - Display search results

2. **Advanced Search**
   - Filter by category, price, featured status
   - Combine multiple filters
   - Pagination

3. **Error Handling**
   - Empty search results
   - API errors
   - Network errors

4. **Performance**
   - Loading states
   - Search debouncing
   - Response time measurement

### Update Profile Scenarios
1. **Profile Display**
   - Load user profile
   - Display formatted data
   - Authentication checks

2. **Profile Editing**
   - Enter/exit edit mode
   - Form field updates
   - Data validation

3. **Profile Update**
   - Successful updates
   - Validation errors
   - Network errors

4. **User Experience**
   - Cancel changes
   - Multiple edit sessions
   - Loading states

## 🎯 Test Quality Metrics

### Coverage Goals
- **Unit Tests**: 90%+ coverage cho components và helpers
- **Integration Tests**: 80%+ coverage cho complete flows
- **API Tests**: 95%+ coverage cho all endpoints

### Test Types
- **Functional Tests**: Verify features work correctly
- **Error Tests**: Handle errors gracefully
- **Performance Tests**: Measure response times
- **Accessibility Tests**: Ensure proper ARIA labels
- **User Experience Tests**: Test complete user journeys

## 🐛 Debugging Tests

### Common Issues
1. **Mock không hoạt động**: Kiểm tra MSW handlers
2. **Async tests fail**: Sử dụng waitFor() đúng cách
3. **Component không render**: Kiểm tra mock dependencies

### Debug Commands
```bash
# Run specific test với debug info
npm test -- --verbose tests/unit/components/search/search-input.test.tsx

# Run tests với debug output
DEBUG=* npm test

# Run single test case
npm test -- --testNamePattern="should complete full search flow"
```

## ✅ Kết luận

Đã triển khai đầy đủ testing suite cho:
- ✅ Search Product functionality
- ✅ Update Profile functionality
- ✅ Unit tests cho tất cả components và helpers
- ✅ Integration tests cho complete user flows
- ✅ API endpoint testing
- ✅ Error handling và edge cases
- ✅ Performance và accessibility testing

Tests đảm bảo cả hai tính năng hoạt động đúng và có thể maintain được trong tương lai.
