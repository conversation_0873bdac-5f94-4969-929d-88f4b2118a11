# Test Features - NS Shop

## 🔍 Tính năng Search đã được khắc phục

### Desktop Search
1. **Vào trang chủ**: http://localhost:3002
2. **Tìm search bar** ở header (giữa logo và các action buttons)
3. **Nhập từ khóa** vào search box (ví dụ: "áo")
4. **Nhấn Enter** hoặc submit form
5. **Kiểm tra**: Trang sẽ chuyển đến `/search?search=áo`

### Mobile Search
1. **Mở mobile view** (thu nhỏ browser hoặc dùng dev tools)
2. **Nhấn vào icon Search** ở header
3. **Mobile menu sẽ mở** với search bar ở cuối
4. **Nhập từ khóa** và nhấn nút "Tìm"
5. **Kiểm tra**: Trang sẽ chuyển đến trang search với từ khóa

## 👤 User Menu Dropdown đã được tạo

### Khi chưa đăng nhập
1. **Vào trang chủ**: http://localhost:3002
2. **Kiểm tra header**: Sẽ thấy nút "Đăng nhập" và "Đăng ký"
3. **Mobile view**: Mở menu sẽ thấy các nút đăng nhập/đăng ký ở cuối

### Khi đã đăng nhập
1. **Đăng nhập**: Vào `/auth/signin`
2. **Kiểm tra header**: Sẽ thấy icon User thay vì nút đăng nhập
3. **Click vào User icon**: Dropdown menu sẽ hiện với:
   - Thông tin user (tên, email)
   - Thông tin cá nhân
   - Đơn hàng của tôi
   - Danh sách yêu thích
   - Quản trị (nếu là admin)
   - Đăng xuất
4. **Mobile view**: Menu sẽ hiển thị thông tin user và các tùy chọn

## 📝 Trang Profile hoạt động

### Truy cập Profile
1. **Đăng nhập** vào hệ thống
2. **Click User menu** → "Thông tin cá nhân"
3. **Hoặc truy cập trực tiếp**: `/profile`

### Chức năng Profile
1. **Xem thông tin**: Hiển thị tên, email, phone, ngày sinh, giới tính
2. **Chỉnh sửa**: Click nút "Chỉnh sửa" để edit thông tin
3. **Cập nhật**: Thay đổi thông tin và click "Lưu thay đổi"
4. **Hủy**: Click "Hủy" để không lưu thay đổi

## 🔧 API Endpoints hoạt động

### Profile API
- **GET /api/profile**: Lấy thông tin user hiện tại
- **PUT /api/profile**: Cập nhật thông tin user

### Search API
- **GET /api/products?search=keyword**: Tìm kiếm sản phẩm

## ✅ Checklist kiểm tra

- [ ] Search bar desktop hoạt động
- [ ] Search bar mobile hoạt động
- [ ] User dropdown menu (desktop) hoạt động
- [ ] User menu mobile hoạt động
- [ ] Trang profile load được
- [ ] Chỉnh sửa profile hoạt động
- [ ] API profile hoạt động
- [ ] Navigation giữa các trang hoạt động

## 🐛 Các vấn đề có thể gặp

1. **Database chưa có user**: Cần tạo account trước
2. **Session không hoạt động**: Kiểm tra NextAuth config
3. **API lỗi**: Kiểm tra database connection
4. **Search không có kết quả**: Cần có sản phẩm trong database

## 📋 Kết luận

Cả hai vấn đề đã được khắc phục:
1. ✅ **Tính năng search hoạt động** - Có thể search từ header và chuyển đến trang search
2. ✅ **Trang profile và user menu hoạt động** - Có dropdown menu và trang profile đầy đủ chức năng
