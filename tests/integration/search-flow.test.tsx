/**
 * Search Flow Integration Tests
 * Kiểm tra integration cho toàn bộ flow search từ input đến hiển thị kết quả
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useRouter } from "next/navigation";
import { Header } from "@/components/layout/header";
import SearchPage from "@/app/search/page";
import { mockProducts, mockApiResponses } from "../fixtures/mock-data";
import { server } from "../mocks/server";
import { http, HttpResponse } from "msw";

// Mock useRouter
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

// Mock useSession
jest.mock("next-auth/react", () => ({
  useSession: jest.fn(() => ({ data: null })),
}));

// Mock useSettingsContext
jest.mock("@/contexts/SettingsContext", () => ({
  useSettingsContext: jest.fn(() => ({
    settings: {
      siteName: "NS Shop",
    },
  })),
}));

// Mock fetch
global.fetch = jest.fn();

const mockPush = jest.fn();
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUseSearchParams = require("next/navigation")
  .useSearchParams as jest.MockedFunction<any>;
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe("Search Flow Integration", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as any);

    // Mock search params
    mockUseSearchParams.mockReturnValue({
      get: jest.fn((key: string) => {
        const params: Record<string, string> = {
          search: "áo",
          category: "",
          page: "1",
          limit: "12",
        };
        return params[key] || null;
      }),
      toString: jest.fn(() => "search=%C3%A1o"),
    });

    // Mock fetch responses
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => mockApiResponses.searchSuccess,
    } as Response);
  });

  describe("Complete Search Flow", () => {
    it("should complete full search flow from header to results", async () => {
      // Setup MSW handler for search API
      server.use(
        http.get("/api/products", ({ request }) => {
          const url = new URL(request.url);
          const searchQuery = url.searchParams.get("search");

          if (searchQuery === "áo") {
            const filteredProducts = mockProducts.filter((p) =>
              p.name.toLowerCase().includes("áo")
            );
            return HttpResponse.json({
              products: filteredProducts,
              pagination: {
                total: filteredProducts.length,
                page: 1,
                limit: 12,
                pages: 1,
              },
            });
          }

          return HttpResponse.json(mockApiResponses.searchEmpty);
        })
      );

      // Render header component
      const { rerender } = render(<Header />);

      // Type in search input
      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      fireEvent.change(searchInput, { target: { value: "áo" } });

      // Submit search
      fireEvent.submit(searchInput.closest("form")!);

      // Verify navigation was called
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=%C3%A1o");
      });

      // Now render search page with the search query
      rerender(<SearchPage />);

      // Wait for search results to load
      await waitFor(() => {
        expect(screen.getByText("Kết quả tìm kiếm")).toBeInTheDocument();
      });

      // Verify search results are displayed
      await waitFor(() => {
        const productNames = mockProducts
          .filter((p) => p.name.toLowerCase().includes("áo"))
          .map((p) => p.name);

        productNames.forEach((name) => {
          expect(screen.getByText(name)).toBeInTheDocument();
        });
      });
    });

    it("should handle empty search results", async () => {
      // Setup MSW handler for empty results
      server.use(
        http.get("/api/products", () => {
          return HttpResponse.json(mockApiResponses.searchEmpty);
        })
      );

      // Mock search params for non-existent query
      mockUseSearchParams.mockReturnValue({
        get: jest.fn((key: string) => {
          const params: Record<string, string> = {
            search: "nonexistent",
            category: "",
            page: "1",
            limit: "12",
          };
          return params[key] || null;
        }),
        toString: jest.fn(() => "search=nonexistent"),
      });

      render(<SearchPage />);

      await waitFor(() => {
        expect(
          screen.getByText("Không tìm thấy sản phẩm nào")
        ).toBeInTheDocument();
      });

      expect(
        screen.getByText(
          "Hãy thử tìm kiếm với từ khóa khác hoặc điều chỉnh bộ lọc"
        )
      ).toBeInTheDocument();
    });

    it("should handle search with filters", async () => {
      // Setup MSW handler for filtered search
      server.use(
        http.get("/api/products", ({ request }) => {
          const url = new URL(request.url);
          const searchQuery = url.searchParams.get("search");
          const category = url.searchParams.get("category");
          const featured = url.searchParams.get("featured");

          let filteredProducts = mockProducts;

          if (searchQuery) {
            filteredProducts = filteredProducts.filter((p) =>
              p.name.toLowerCase().includes(searchQuery.toLowerCase())
            );
          }

          if (category) {
            filteredProducts = filteredProducts.filter(
              (p) => p.categoryId === category
            );
          }

          if (featured === "true") {
            filteredProducts = filteredProducts.filter((p) => p.featured);
          }

          return HttpResponse.json({
            products: filteredProducts,
            pagination: {
              total: filteredProducts.length,
              page: 1,
              limit: 12,
              pages: 1,
            },
          });
        })
      );

      // Mock search params with filters
      mockUseSearchParams.mockReturnValue({
        get: jest.fn((key: string) => {
          const params: Record<string, string> = {
            search: "áo",
            category: "cat-1",
            featured: "true",
            page: "1",
            limit: "12",
          };
          return params[key] || null;
        }),
        toString: jest.fn(() => "search=%C3%A1o&category=cat-1&featured=true"),
      });

      render(<SearchPage />);

      await waitFor(() => {
        expect(screen.getByText("Kết quả tìm kiếm")).toBeInTheDocument();
      });

      // Verify filtered results
      await waitFor(() => {
        const filteredProducts = mockProducts.filter(
          (p) =>
            p.name.toLowerCase().includes("áo") &&
            p.categoryId === "cat-1" &&
            p.featured
        );

        expect(
          screen.getByText(`Tìm thấy ${filteredProducts.length} sản phẩm`)
        ).toBeInTheDocument();
      });
    });

    it("should handle pagination in search results", async () => {
      const allProducts = [...mockProducts, ...mockProducts, ...mockProducts]; // Create more products

      server.use(
        http.get("/api/products", ({ request }) => {
          const url = new URL(request.url);
          const page = parseInt(url.searchParams.get("page") || "1");
          const limit = parseInt(url.searchParams.get("limit") || "12");
          const skip = (page - 1) * limit;

          const paginatedProducts = allProducts.slice(skip, skip + limit);

          return HttpResponse.json({
            products: paginatedProducts,
            pagination: {
              total: allProducts.length,
              page,
              limit,
              pages: Math.ceil(allProducts.length / limit),
            },
          });
        })
      );

      render(<SearchPage />);

      await waitFor(() => {
        expect(screen.getByText("Kết quả tìm kiếm")).toBeInTheDocument();
      });

      // Should show pagination info
      await waitFor(() => {
        expect(
          screen.getByText(`Tìm thấy ${allProducts.length} sản phẩm`)
        ).toBeInTheDocument();
      });
    });

    it("should handle API errors gracefully", async () => {
      // Setup MSW handler for API error
      server.use(
        http.get("/api/products", () => {
          return HttpResponse.json(
            { error: "Internal server error" },
            { status: 500 }
          );
        })
      );

      render(<SearchPage />);

      await waitFor(() => {
        expect(
          screen.getByText("Có lỗi xảy ra khi tìm kiếm sản phẩm")
        ).toBeInTheDocument();
      });
    });

    it("should handle network errors", async () => {
      // Setup MSW handler for network error
      server.use(
        http.get("/api/products", () => {
          return HttpResponse.error();
        })
      );

      render(<SearchPage />);

      await waitFor(() => {
        expect(
          screen.getByText("Có lỗi xảy ra khi tìm kiếm sản phẩm")
        ).toBeInTheDocument();
      });
    });
  });

  describe("Advanced Search Features", () => {
    it("should toggle advanced search panel", async () => {
      server.use(
        http.get("/api/products", () => {
          return HttpResponse.json(mockApiResponses.searchSuccess);
        }),
        http.get("/api/categories", () => {
          return HttpResponse.json(mockApiResponses.categoriesSuccess);
        })
      );

      render(<SearchPage />);

      await waitFor(() => {
        const advancedSearchButton = screen.getByText("Hiện tìm kiếm nâng cao");
        expect(advancedSearchButton).toBeInTheDocument();
      });

      // Click to show advanced search
      const advancedSearchButton = screen.getByText("Hiện tìm kiếm nâng cao");
      fireEvent.click(advancedSearchButton);

      await waitFor(() => {
        expect(screen.getByText("Tìm kiếm nâng cao")).toBeInTheDocument();
      });

      // Should show filter options
      expect(screen.getByText("Từ khóa tìm kiếm")).toBeInTheDocument();
      expect(screen.getByText("Danh mục")).toBeInTheDocument();
    });

    it("should apply advanced search filters", async () => {
      let lastRequestUrl: string = "";

      server.use(
        http.get("/api/products", ({ request }) => {
          lastRequestUrl = request.url;
          return HttpResponse.json(mockApiResponses.searchSuccess);
        }),
        http.get("/api/categories", () => {
          return HttpResponse.json(mockApiResponses.categoriesSuccess);
        })
      );

      render(<SearchPage />);

      // Open advanced search
      await waitFor(() => {
        const advancedSearchButton = screen.getByText("Hiện tìm kiếm nâng cao");
        fireEvent.click(advancedSearchButton);
      });

      // Fill in filters
      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
        fireEvent.change(searchInput, { target: { value: "áo thun" } });
      });

      // Submit advanced search
      const searchButton = screen.getByText("Tìm kiếm");
      fireEvent.click(searchButton);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          expect.stringContaining("search=")
        );
      });
    });
  });

  describe("Search Results Display", () => {
    beforeEach(() => {
      server.use(
        http.get("/api/products", () => {
          return HttpResponse.json(mockApiResponses.searchSuccess);
        })
      );
    });

    it("should display product cards with correct information", async () => {
      render(<SearchPage />);

      await waitFor(() => {
        const products = mockApiResponses.searchSuccess.products;

        products.forEach((product) => {
          expect(screen.getByText(product.name)).toBeInTheDocument();

          // Check price display
          if (product.salePrice) {
            expect(
              screen.getByText(product.salePrice.toLocaleString())
            ).toBeInTheDocument();
          } else {
            expect(
              screen.getByText(product.price.toLocaleString())
            ).toBeInTheDocument();
          }
        });
      });
    });

    it("should toggle between grid and list view", async () => {
      render(<SearchPage />);

      await waitFor(() => {
        expect(screen.getByText("Kết quả tìm kiếm")).toBeInTheDocument();
      });

      // Should have view toggle buttons
      const gridButton = screen.getByRole("button", { name: /grid/i });
      const listButton = screen.getByRole("button", { name: /list/i });

      expect(gridButton).toBeInTheDocument();
      expect(listButton).toBeInTheDocument();

      // Toggle to list view
      fireEvent.click(listButton);

      // Verify view changed (this would depend on actual implementation)
      // For now, just verify the button was clicked
      expect(listButton).toBeInTheDocument();
    });

    it("should handle product interactions", async () => {
      render(<SearchPage />);

      await waitFor(() => {
        const products = mockApiResponses.searchSuccess.products;
        const firstProduct = products[0];

        // Should have product link
        const productLink = screen.getByText(firstProduct.name).closest("a");
        expect(productLink).toHaveAttribute(
          "href",
          `/products/${firstProduct.slug}`
        );
      });
    });
  });

  describe("Search Performance", () => {
    it("should show loading state during search", async () => {
      // Delay the API response
      server.use(
        http.get("/api/products", async () => {
          await new Promise((resolve) => setTimeout(resolve, 100));
          return HttpResponse.json(mockApiResponses.searchSuccess);
        })
      );

      render(<SearchPage />);

      // Should show loading state initially
      expect(screen.getByText("Đang tải...")).toBeInTheDocument();

      // Wait for results to load
      await waitFor(
        () => {
          expect(screen.getByText("Kết quả tìm kiếm")).toBeInTheDocument();
        },
        { timeout: 2000 }
      );
    });

    it("should debounce search input", async () => {
      let requestCount = 0;

      server.use(
        http.get("/api/products", () => {
          requestCount++;
          return HttpResponse.json(mockApiResponses.searchSuccess);
        })
      );

      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      // Type multiple characters quickly
      fireEvent.change(searchInput, { target: { value: "a" } });
      fireEvent.change(searchInput, { target: { value: "ao" } });
      fireEvent.change(searchInput, { target: { value: "áo" } });

      // Submit only once
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledTimes(1);
      });
    });
  });
});
