/**
 * Products API Integration Tests
 * Kiểm tra integration cho các API routes products
 */

import { NextRequest } from "next/server";
import { GET, POST } from "@/app/api/products/route";
import { GET as GetProduct } from "@/app/api/products/[slug]/route";
import { prisma } from "@/lib/prisma";

// Mocks are set up globally in setup.ts
const mockPrisma = prisma as any;

// Mock NextAuth
jest.mock("next-auth/next", () => ({
  getServerSession: jest.fn(),
}));

describe("Products API Integration Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("GET /api/products", () => {
    it("should fetch products with pagination", async () => {
      const mockProducts = [
        {
          id: "product_1",
          name: "Áo thun nam",
          slug: "ao-thun-nam",
          description: "Áo thun cotton cao cấp",
          price: 299000,
          salePrice: 199000,
          stock: 50,
          images: ["image1.jpg", "image2.jpg"],
          categoryId: "cat_1",
          featured: true,
          status: "ACTIVE",
          createdAt: new Date(),
          updatedAt: new Date(),
          category: {
            id: "cat_1",
            name: "Áo thun",
            slug: "ao-thun",
          },
        },
        {
          id: "product_2",
          name: "Quần jeans nữ",
          slug: "quan-jeans-nu",
          description: "Quần jeans skinny fit",
          price: 599000,
          salePrice: null,
          stock: 30,
          images: ["image3.jpg"],
          categoryId: "cat_2",
          featured: false,
          status: "ACTIVE",
          createdAt: new Date(),
          updatedAt: new Date(),
          category: {
            id: "cat_2",
            name: "Quần jeans",
            slug: "quan-jeans",
          },
        },
      ];

      // Setup mocks
      mockPrisma.product.findMany.mockResolvedValue(mockProducts);
      mockPrisma.product.count.mockResolvedValue(2);

      // Create request
      const request = new NextRequest(
        "http://localhost:3000/api/products?page=1&limit=10"
      );

      // Call API
      const response = await GET(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(responseData.products).toHaveLength(2);
      expect(responseData.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1,
      });

      // Verify database call
      expect(mockPrisma.product.findMany).toHaveBeenCalledWith({
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
        where: {
          status: "ACTIVE",
        },
        orderBy: {
          createdAt: "desc",
        },
        skip: 0,
        take: 10,
      });
    });

    it("should filter products by category", async () => {
      const mockProducts = [
        {
          id: "product_1",
          name: "Áo thun nam",
          slug: "ao-thun-nam",
          description: "Áo thun cotton cao cấp",
          price: 299000,
          salePrice: 199000,
          stock: 50,
          images: ["image1.jpg"],
          categoryId: "cat_1",
          featured: true,
          status: "ACTIVE",
          createdAt: new Date(),
          updatedAt: new Date(),
          category: {
            id: "cat_1",
            name: "Áo thun",
            slug: "ao-thun",
          },
        },
      ];

      // Setup mocks
      mockPrisma.product.findMany.mockResolvedValue(mockProducts);
      mockPrisma.product.count.mockResolvedValue(1);

      // Create request with category filter
      const request = new NextRequest(
        "http://localhost:3000/api/products?category=ao-thun"
      );

      // Call API
      const response = await GET(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(responseData.products).toHaveLength(1);
      expect(responseData.products[0].category.slug).toBe("ao-thun");

      // Verify database call includes category filter
      expect(mockPrisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            status: "ACTIVE",
            category: {
              slug: "ao-thun",
            },
          },
        })
      );
    });

    it("should search products by name", async () => {
      const mockProducts = [
        {
          id: "product_1",
          name: "Áo thun nam",
          slug: "ao-thun-nam",
          description: "Áo thun cotton cao cấp",
          price: 299000,
          salePrice: null,
          stock: 50,
          images: ["image1.jpg"],
          categoryId: "cat_1",
          featured: true,
          status: "ACTIVE",
          createdAt: new Date(),
          updatedAt: new Date(),
          category: {
            id: "cat_1",
            name: "Áo thun",
            slug: "ao-thun",
          },
        },
      ];

      // Setup mocks
      mockPrisma.product.findMany.mockResolvedValue(mockProducts);
      mockPrisma.product.count.mockResolvedValue(1);

      // Create request with search query
      const request = new NextRequest(
        "http://localhost:3000/api/products?search=áo thun"
      );

      // Call API
      const response = await GET(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(responseData.products).toHaveLength(1);
      expect(responseData.products[0].name).toContain("Áo thun");

      // Verify database call includes search filter
      expect(mockPrisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            status: "ACTIVE",
            OR: [
              {
                name: {
                  contains: "áo thun",
                  mode: "insensitive",
                },
              },
              {
                description: {
                  contains: "áo thun",
                  mode: "insensitive",
                },
              },
            ],
          },
        })
      );
    });

    it("should filter products by price range", async () => {
      const mockProducts = [
        {
          id: "product_1",
          name: "Áo thun nam",
          slug: "ao-thun-nam",
          description: "Áo thun cotton cao cấp",
          price: 299000,
          salePrice: null,
          stock: 50,
          images: ["image1.jpg"],
          categoryId: "cat_1",
          featured: true,
          status: "ACTIVE",
          createdAt: new Date(),
          updatedAt: new Date(),
          category: {
            id: "cat_1",
            name: "Áo thun",
            slug: "ao-thun",
          },
        },
      ];

      // Setup mocks
      mockPrisma.product.findMany.mockResolvedValue(mockProducts);
      mockPrisma.product.count.mockResolvedValue(1);

      // Create request with price range
      const request = new NextRequest(
        "http://localhost:3000/api/products?minPrice=200000&maxPrice=500000"
      );

      // Call API
      const response = await GET(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(responseData.products).toHaveLength(1);

      // Verify database call includes price filter
      expect(mockPrisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            status: "ACTIVE",
            price: {
              gte: 200000,
              lte: 500000,
            },
          },
        })
      );
    });

    it("should sort products by different criteria", async () => {
      const mockProducts: any[] = [];

      // Setup mocks
      mockPrisma.product.findMany.mockResolvedValue(mockProducts);
      mockPrisma.product.count.mockResolvedValue(0);

      // Test different sort options
      const sortOptions = [
        { sort: "price_asc", expected: { price: "asc" } },
        { sort: "price_desc", expected: { price: "desc" } },
        { sort: "name_asc", expected: { name: "asc" } },
        { sort: "name_desc", expected: { name: "desc" } },
        { sort: "newest", expected: { createdAt: "desc" } },
        { sort: "oldest", expected: { createdAt: "asc" } },
      ];

      for (const { sort, expected } of sortOptions) {
        mockPrisma.product.findMany.mockClear();

        const request = new NextRequest(
          `http://localhost:3000/api/products?sort=${sort}`
        );
        await GET(request);

        expect(mockPrisma.product.findMany).toHaveBeenCalledWith(
          expect.objectContaining({
            orderBy: expected,
          })
        );
      }
    });

    it("should handle database errors gracefully", async () => {
      // Setup mock to throw error
      mockPrisma.product.findMany.mockRejectedValue(
        new Error("Database connection failed")
      );

      // Create request
      const request = new NextRequest("http://localhost:3000/api/products");

      // Call API
      const response = await GET(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(500);
      expect(responseData.error).toBe("Internal server error");
    });
  });

  describe("GET /api/products/[slug]", () => {
    it("should fetch product by slug", async () => {
      const mockProduct = {
        id: "product_1",
        name: "Áo thun nam",
        slug: "ao-thun-nam",
        description: "Áo thun cotton cao cấp",
        price: 299000,
        salePrice: 199000,
        stock: 50,
        images: ["image1.jpg", "image2.jpg"],
        categoryId: "cat_1",
        featured: true,
        status: "ACTIVE",
        createdAt: new Date(),
        updatedAt: new Date(),
        category: {
          id: "cat_1",
          name: "Áo thun",
          slug: "ao-thun",
        },
      };

      // Setup mocks
      mockPrisma.product.findUnique.mockResolvedValue(mockProduct);

      // Call API
      const response = await GetProduct(
        new NextRequest("http://localhost:3000/api/products/ao-thun-nam"),
        { params: { slug: "ao-thun-nam" } }
      );
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(responseData.product.slug).toBe("ao-thun-nam");
      expect(responseData.product.name).toBe("Áo thun nam");

      // Verify database call
      expect(mockPrisma.product.findUnique).toHaveBeenCalledWith({
        where: {
          slug: "ao-thun-nam",
          status: "ACTIVE",
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });
    });

    it("should return 404 for non-existent product", async () => {
      // Setup mock to return null
      mockPrisma.product.findUnique.mockResolvedValue(null);

      // Call API
      const response = await GetProduct(
        new NextRequest("http://localhost:3000/api/products/non-existent"),
        { params: { slug: "non-existent" } }
      );
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(404);
      expect(responseData.error).toBe("Product not found");
    });

    it("should handle database errors", async () => {
      // Setup mock to throw error
      mockPrisma.product.findUnique.mockRejectedValue(
        new Error("Database error")
      );

      // Call API
      const response = await GetProduct(
        new NextRequest("http://localhost:3000/api/products/ao-thun-nam"),
        { params: { slug: "ao-thun-nam" } }
      );
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(500);
      expect(responseData.error).toBe("Internal server error");
    });
  });

  describe("Query Parameter Validation", () => {
    it("should handle invalid pagination parameters", async () => {
      mockPrisma.product.findMany.mockResolvedValue([]);
      mockPrisma.product.count.mockResolvedValue(0);

      // Test with invalid page number
      const request = new NextRequest(
        "http://localhost:3000/api/products?page=invalid&limit=abc"
      );
      const response = await GET(request);

      // Should handle gracefully and use defaults
      expect(response.status).toBe(200);
      expect(mockPrisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 0, // Default page 1
          take: 10, // Default limit
        })
      );
    });

    it("should handle invalid price range parameters", async () => {
      mockPrisma.product.findMany.mockResolvedValue([]);
      mockPrisma.product.count.mockResolvedValue(0);

      // Test with invalid price values
      const request = new NextRequest(
        "http://localhost:3000/api/products?minPrice=invalid&maxPrice=abc"
      );
      const response = await GET(request);

      // Should handle gracefully and ignore invalid filters
      expect(response.status).toBe(200);
      expect(mockPrisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            status: "ACTIVE",
            // Should not include price filter
          },
        })
      );
    });
  });
});
