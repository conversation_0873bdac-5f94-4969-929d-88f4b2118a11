/**
 * Authentication API Integration Tests
 * Kiểm tra integration cho các API routes authentication
 */

import { NextRequest } from "next/server";
import { POST } from "@/app/api/auth/register/route";
import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs";

// Get mocked instances (mocks are set up globally in setup.ts)
const mockPrisma = prisma as any;
const mockBcrypt = bcrypt as any;

describe("Authentication API Integration Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("POST /api/auth/register", () => {
    it("should register a new user successfully", async () => {
      // Mock data
      const userData = {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "password123",
      };

      const hashedPassword = "hashed_password_123";
      const createdUser = {
        id: "user_123",
        name: userData.name,
        email: userData.email,
        password: hashedPassword,
        role: "USER",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Setup mocks
      mockPrisma.user.findUnique.mockResolvedValue(null); // User doesn't exist
      mockBcrypt.hash.mockResolvedValue(hashedPassword);
      mockPrisma.user.create.mockResolvedValue(createdUser);

      // Create request
      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          body: JSON.stringify(userData),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Call API
      const response = await POST(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(201);
      expect(responseData.message).toBe("User created successfully");
      expect(responseData.user).toEqual({
        id: createdUser.id,
        name: createdUser.name,
        email: createdUser.email,
        role: createdUser.role,
      });

      // Verify database calls
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: userData.email },
      });
      expect(mockBcrypt.hash).toHaveBeenCalledWith(userData.password, 12);
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: {
          name: userData.name,
          email: userData.email,
          password: hashedPassword,
        },
      });
    });

    it("should reject registration with existing email", async () => {
      // Mock existing user
      const existingUser = {
        id: "existing_user",
        email: "<EMAIL>",
        name: "Existing User",
        password: "hashed_password",
        role: "USER",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const userData = {
        name: "John Doe",
        email: "<EMAIL>",
        password: "password123",
      };

      // Setup mocks
      mockPrisma.user.findUnique.mockResolvedValue(existingUser);

      // Create request
      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          body: JSON.stringify(userData),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Call API
      const response = await POST(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(400);
      expect(responseData.error).toBe("User already exists");

      // Verify only findUnique was called
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: userData.email },
      });
      expect(mockBcrypt.hash).not.toHaveBeenCalled();
      expect(mockPrisma.user.create).not.toHaveBeenCalled();
    });

    it("should validate required fields", async () => {
      const invalidData = {
        name: "",
        email: "invalid-email",
        password: "123", // Too short
      };

      // Create request
      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          body: JSON.stringify(invalidData),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Call API
      const response = await POST(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(400);
      expect(responseData.error).toContain("validation");

      // Verify no database calls were made
      expect(mockPrisma.user.findUnique).not.toHaveBeenCalled();
      expect(mockPrisma.user.create).not.toHaveBeenCalled();
    });

    it("should handle missing request body", async () => {
      // Create request without body
      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Call API
      const response = await POST(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(400);
      expect(responseData.error).toBeDefined();
    });

    it("should handle database errors", async () => {
      const userData = {
        name: "John Doe",
        email: "<EMAIL>",
        password: "password123",
      };

      // Setup mocks to throw error
      mockPrisma.user.findUnique.mockRejectedValue(
        new Error("Database connection failed")
      );

      // Create request
      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          body: JSON.stringify(userData),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Call API
      const response = await POST(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(500);
      expect(responseData.error).toBe("Internal server error");
    });

    it("should handle bcrypt hashing errors", async () => {
      const userData = {
        name: "John Doe",
        email: "<EMAIL>",
        password: "password123",
      };

      // Setup mocks
      mockPrisma.user.findUnique.mockResolvedValue(null);
      mockBcrypt.hash.mockRejectedValue(new Error("Hashing failed"));

      // Create request
      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          body: JSON.stringify(userData),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Call API
      const response = await POST(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(500);
      expect(responseData.error).toBe("Internal server error");
    });

    it("should sanitize user data in response", async () => {
      const userData = {
        name: "John Doe",
        email: "<EMAIL>",
        password: "password123",
      };

      const hashedPassword = "hashed_password_123";
      const createdUser = {
        id: "user_123",
        name: userData.name,
        email: userData.email,
        password: hashedPassword,
        role: "USER",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Setup mocks
      mockPrisma.user.findUnique.mockResolvedValue(null);
      mockBcrypt.hash.mockResolvedValue(hashedPassword);
      mockPrisma.user.create.mockResolvedValue(createdUser);

      // Create request
      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          body: JSON.stringify(userData),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Call API
      const response = await POST(request);
      const responseData = await response.json();

      // Assertions - password should not be in response
      expect(responseData.user.password).toBeUndefined();
      expect(responseData.user.id).toBeDefined();
      expect(responseData.user.name).toBe(userData.name);
      expect(responseData.user.email).toBe(userData.email);
      expect(responseData.user.role).toBe("USER");
    });
  });

  describe("Request Validation", () => {
    it("should validate Content-Type header", async () => {
      const userData = {
        name: "John Doe",
        email: "<EMAIL>",
        password: "password123",
      };

      // Create request with wrong content type
      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          body: JSON.stringify(userData),
          headers: {
            "Content-Type": "text/plain",
          },
        }
      );

      // Call API
      const response = await POST(request);

      // Should handle gracefully
      expect(response.status).toBeGreaterThanOrEqual(400);
    });

    it("should handle malformed JSON", async () => {
      // Create request with malformed JSON
      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          body: "{ invalid json }",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Call API
      const response = await POST(request);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(400);
      expect(responseData.error).toBeDefined();
    });
  });
});
