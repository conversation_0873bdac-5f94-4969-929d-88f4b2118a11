/**
 * Manual Tests Runner
 * <PERSON><PERSON><PERSON> t<PERSON><PERSON> cả manual tests theo thứ tự
 */

const { execSync } = require('child_process');
const path = require('path');

const tests = [
  {
    name: 'Authentication Integration Test',
    file: 'auth-integration.js',
    description: 'Test authentication flow với database'
  },
  {
    name: 'Sign-in Integration Test', 
    file: 'signin-integration.js',
    description: 'Test sign-in API endpoint'
  },
  {
    name: 'Profile Update Integration Test',
    file: 'profile-update-integration.js', 
    description: 'Test profile update functionality'
  }
];

async function runManualTests() {
  console.log('🧪 Running Manual Integration Tests...\n');
  
  for (const test of tests) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`🔍 ${test.name}`);
    console.log(`📝 ${test.description}`);
    console.log(`${'='.repeat(60)}\n`);
    
    try {
      const testPath = path.join(__dirname, test.file);
      execSync(`node ${testPath}`, { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log(`\n✅ ${test.name} completed\n`);
    } catch (error) {
      console.error(`\n❌ ${test.name} failed:`, error.message);
      console.log(`\n⚠️  Continuing with next test...\n`);
    }
  }
  
  console.log(`\n${'='.repeat(60)}`);
  console.log('🎉 All manual tests completed!');
  console.log(`${'='.repeat(60)}\n`);
}

// Chạy nếu file được gọi trực tiếp
if (require.main === module) {
  runManualTests().catch(console.error);
}

module.exports = { runManualTests };
