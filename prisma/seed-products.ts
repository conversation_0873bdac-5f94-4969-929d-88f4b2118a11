import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function seedProducts() {
  console.log("🌱 Seeding products...");

  try {
    // First, ensure we have categories
    let category = await prisma.category.findFirst({
      where: { slug: "ao-nu" },
    });

    if (!category) {
      category = await prisma.category.create({
        data: {
          name: "Áo nữ",
          slug: "ao-nu",
          description: "Các loại áo dành cho nữ",
        },
      });
    }

    // Create sample products
    const products = [
      {
        name: "Áo Blouse Hoa Nhí Vintage",
        slug: "ao-blouse-hoa-nhi-vintage",
        description:
          "Áo blouse với họa tiết hoa nhí vintage, chất liệu voan mềm mại, phù hợp cho nhiều dịp khác nhau. Thiết kế thanh lịch và nữ tính.",
        price: 299000,
        salePrice: 249000,
        sku: "BL001",
        stock: 15,
        categoryId: category.id,
        images: [
          "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500",
          "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600",
        ],
        featured: true,
        status: "ACTIVE" as const,
        tags: ["vintage", "hoa nhí", "blouse", "nữ tính"],
      },
      {
        name: "Váy Midi Hoa Cúc",
        slug: "vay-midi-hoa-cuc",
        description:
          "Váy midi với họa tiết hoa cúc tươi tắn, chất liệu cotton thoáng mát. Thiết kế A-line tôn dáng, phù hợp cho mùa hè.",
        price: 399000,
        salePrice: 329000,
        sku: "DR001",
        stock: 8,
        categoryId: category.id,
        images: [
          "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=500",
          "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=500&h=600",
        ],
        featured: true,
        status: "ACTIVE" as const,
        tags: ["midi", "hoa cúc", "váy", "mùa hè"],
      },
      {
        name: "Áo Sơ Mi Trắng Basic",
        slug: "ao-so-mi-trang-basic",
        description:
          "Áo sơ mi trắng basic, thiết kế đơn giản nhưng tinh tế. Chất liệu cotton cao cấp, dễ phối đồ cho nhiều phong cách khác nhau.",
        price: 199000,
        sku: "SH001",
        stock: 25,
        categoryId: category.id,
        images: [
          "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500",
          "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500&h=600",
        ],
        featured: false,
        status: "ACTIVE" as const,
        tags: ["basic", "sơ mi", "trắng", "công sở"],
      },
      {
        name: "Đầm Maxi Bohemian",
        slug: "dam-maxi-bohemian",
        description:
          "Đầm maxi phong cách bohemian với họa tiết độc đáo. Chất liệu chiffon nhẹ nhàng, thoải mái cho những chuyến du lịch.",
        price: 599000,
        salePrice: 499000,
        sku: "DR002",
        stock: 12,
        categoryId: category.id,
        images: [
          "https://images.unsplash.com/photo-1572804013309-59a88b7e92f1?w=500",
          "https://images.unsplash.com/photo-1572804013309-59a88b7e92f1?w=500&h=600",
        ],
        featured: true,
        status: "ACTIVE" as const,
        tags: ["maxi", "bohemian", "du lịch", "chiffon"],
      },
      {
        name: "Áo Len Cổ Lọ",
        slug: "ao-len-co-lo",
        description:
          "Áo len cổ lọ ấm áp cho mùa đông. Chất liệu len cao cấp, thiết kế ôm vừa phải, tạo silhouette đẹp.",
        price: 349000,
        sku: "SW001",
        stock: 18,
        categoryId: category.id,
        images: [
          "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500",
          "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&h=600",
        ],
        featured: false,
        status: "ACTIVE" as const,
        tags: ["len", "cổ lọ", "mùa đông", "ấm áp"],
      },
    ];

    // Create products (skip if already exists)
    for (const productData of products) {
      const existingProduct = await prisma.product.findUnique({
        where: { slug: productData.slug },
      });

      if (!existingProduct) {
        await prisma.product.create({
          data: productData,
        });
        console.log(`✅ Created product: ${productData.name}`);
      } else {
        console.log(`⏭️ Product already exists: ${productData.name}`);
      }
    }

    console.log(`✅ Created ${products.length} sample products`);
  } catch (error) {
    console.error("❌ Error seeding products:", error);
    throw error;
  }
}

if (require.main === module) {
  seedProducts()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedProducts };
