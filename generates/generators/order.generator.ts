import { faker } from "@faker-js/faker";
import { BaseGenerator, GeneratorConfig } from "./base/base.generator";

export interface OrderItemData {
  productId: string;
  quantity: number;
  price: number;
  total: number;
}

export interface AddressData {
  fullName: string;
  phone: string;
  address: string;
  ward: string;
  district: string;
  province: string;
}

export interface OrderData {
  userId: string;
  items: OrderItemData[];
  total: number;
  status:
    | "PENDING"
    | "CONFIRMED"
    | "PROCESSING"
    | "SHIPPED"
    | "DELIVERED"
    | "CANCELLED";
  paymentMethod: "COD" | "BANK_TRANSFER" | "CREDIT_CARD";
  paymentStatus: "PENDING" | "PAID" | "FAILED" | "REFUNDED";
  shippingAddress: AddressData;
  billingAddress: AddressData;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderGeneratorConfig extends GeneratorConfig {
  userIds?: string[];
  productIds?: string[];
  statusDistribution?: {
    PENDING: number;
    CONFIRMED: number;
    PROCESSING: number;
    SHIPPED: number;
    DELIVERED: number;
    CANCELLED: number;
  };
  paymentMethodDistribution?: {
    COD: number;
    BANK_TRANSFER: number;
    CREDIT_CARD: number;
  };
  minItems?: number;
  maxItems?: number;
  minTotal?: number;
  maxTotal?: number;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export class OrderGenerator extends BaseGenerator<OrderData> {
  private availableUsers: any[] = [];
  private availableProducts: any[] = [];

  constructor(config: OrderGeneratorConfig = {}) {
    super("Order", config);
    this.config = {
      statusDistribution: {
        PENDING: 10,
        CONFIRMED: 15,
        PROCESSING: 20,
        SHIPPED: 25,
        DELIVERED: 25,
        CANCELLED: 5,
      },
      paymentMethodDistribution: {
        COD: 60,
        BANK_TRANSFER: 25,
        CREDIT_CARD: 15,
      },
      minItems: 1,
      maxItems: 5,
      minTotal: 100000,
      maxTotal: 5000000,
      dateRange: {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        end: new Date(),
      },
      ...config,
    };
  }

  private get orderConfig(): OrderGeneratorConfig {
    return this.config as OrderGeneratorConfig;
  }

  private async loadUsersAndProducts(): Promise<void> {
    // Load users
    if (this.orderConfig.userIds && this.orderConfig.userIds.length > 0) {
      this.availableUsers = await this.prisma.user.findMany({
        where: {
          id: { in: this.orderConfig.userIds },
        },
      });
    } else {
      this.availableUsers = await this.prisma.user.findMany({
        where: {
          role: "USER",
        },
      });
    }

    // Load products
    if (this.orderConfig.productIds && this.orderConfig.productIds.length > 0) {
      this.availableProducts = await this.prisma.product.findMany({
        where: {
          id: { in: this.orderConfig.productIds },
        },
      });
    } else {
      this.availableProducts = await this.prisma.product.findMany({
        where: {
          status: "ACTIVE",
          stock: { gt: 0 },
        },
      });
    }

    if (this.availableUsers.length === 0) {
      throw new Error("No users available for order generation");
    }

    if (this.availableProducts.length === 0) {
      throw new Error("No products available for order generation");
    }
  }

  private generateStatus():
    | "PENDING"
    | "CONFIRMED"
    | "PROCESSING"
    | "SHIPPED"
    | "DELIVERED"
    | "CANCELLED" {
    const distribution = this.orderConfig.statusDistribution!;
    const random = Math.random() * 100;

    let cumulative = 0;
    for (const [status, percentage] of Object.entries(distribution)) {
      cumulative += percentage;
      if (random <= cumulative) {
        return status as any;
      }
    }

    return "DELIVERED"; // Fallback
  }

  private generatePaymentMethod(): "COD" | "BANK_TRANSFER" | "CREDIT_CARD" {
    const distribution = this.orderConfig.paymentMethodDistribution!;
    const random = Math.random() * 100;

    let cumulative = 0;
    for (const [method, percentage] of Object.entries(distribution)) {
      cumulative += percentage;
      if (random <= cumulative) {
        return method as any;
      }
    }

    return "COD"; // Fallback
  }

  private generateVietnameseAddress(
    userName: string,
    userPhone?: string
  ): AddressData {
    const provinces = [
      "TP.HCM",
      "Hà Nội",
      "Đà Nẵng",
      "Hải Phòng",
      "Cần Thơ",
      "An Giang",
      "Bà Rịa - Vũng Tàu",
      "Bắc Giang",
      "Bắc Ninh",
      "Bến Tre",
    ];

    const districts = [
      "Quận 1",
      "Quận 2",
      "Quận 3",
      "Quận 4",
      "Quận 5",
      "Quận Bình Thạnh",
      "Quận Tân Bình",
      "Quận Phú Nhuận",
      "Huyện Hóc Môn",
      "Huyện Củ Chi",
    ];

    const wards = [
      "Phường 1",
      "Phường 2",
      "Phường 3",
      "Phường 4",
      "Phường 5",
      "Phường Bến Nghé",
      "Phường Đa Kao",
      "Phường Cô Giang",
      "Xã Tân Phú",
      "Xã Phước Kiển",
    ];

    const streetTypes = ["Đường", "Phố", "Ngõ", "Hẻm"];
    const streetNames = [
      "Lê Lợi",
      "Nguyễn Huệ",
      "Trần Hưng Đạo",
      "Lý Tự Trọng",
      "Hai Bà Trưng",
    ];

    return {
      fullName: userName,
      phone: userPhone || `09${faker.string.numeric(8)}`,
      address: `${faker.number.int({ min: 1, max: 999 })} ${faker.helpers.arrayElement(streetTypes)} ${faker.helpers.arrayElement(streetNames)}`,
      ward: faker.helpers.arrayElement(wards),
      district: faker.helpers.arrayElement(districts),
      province: faker.helpers.arrayElement(provinces),
    };
  }

  private generateOrderItems(): OrderItemData[] {
    const itemCount = faker.number.int({
      min: this.orderConfig.minItems || 1,
      max: this.orderConfig.maxItems || 5,
    });

    const selectedProducts = faker.helpers.arrayElements(
      this.availableProducts,
      itemCount
    );
    const items: OrderItemData[] = [];

    for (const product of selectedProducts) {
      const quantity = faker.number.int({ min: 1, max: 3 });
      const price = product.salePrice || product.price;
      const total = price * quantity;

      items.push({
        productId: product.id,
        quantity,
        price,
        total,
      });
    }

    return items;
  }

  private calculateShippingFee(total: number): number {
    // Free shipping for orders over 500,000 VND
    if (total >= 500000) {
      return 0;
    }

    // Standard shipping fee
    return 30000;
  }

  private generateDiscount(total: number): number {
    // 20% chance of having discount
    if (Math.random() > 0.8) {
      const discountPercent = faker.number.int({ min: 5, max: 20 });
      return Math.round((total * discountPercent) / 100);
    }

    return 0;
  }

  private generateOrderDate(): Date {
    const { start, end } = this.orderConfig.dateRange!;
    return faker.date.between({ from: start, to: end });
  }

  private generatePaymentStatus(
    paymentMethod: string,
    status: string
  ): "PENDING" | "PAID" | "FAILED" | "REFUNDED" {
    if (status === "CANCELLED") {
      return Math.random() > 0.5 ? "FAILED" : "REFUNDED";
    }

    if (paymentMethod === "COD") {
      return status === "DELIVERED" ? "PAID" : "PENDING";
    }

    // For other payment methods
    if (status === "PENDING") {
      return "PENDING";
    }

    return Math.random() > 0.1 ? "PAID" : "FAILED";
  }

  async generateData(): Promise<OrderData[]> {
    await this.loadUsersAndProducts();

    const orders: OrderData[] = [];

    for (let i = 0; i < (this.orderConfig.count || 10); i++) {
      const user = faker.helpers.arrayElement(this.availableUsers);
      const items = this.generateOrderItems();
      const subtotal = items.reduce((sum, item) => sum + item.total, 0);
      const discount = this.generateDiscount(subtotal);
      const shippingFee = this.calculateShippingFee(subtotal - discount);
      const total = subtotal - discount + shippingFee;

      const status = this.generateStatus();
      const paymentMethod = this.generatePaymentMethod();
      const paymentStatus = this.generatePaymentStatus(paymentMethod, status);

      const shippingAddress = this.generateVietnameseAddress(
        user.name,
        user.phone
      );
      const billingAddress =
        Math.random() > 0.7
          ? this.generateVietnameseAddress(user.name, user.phone)
          : shippingAddress; // 70% same as shipping address

      const createdAt = this.generateOrderDate();
      const updatedAt = new Date(
        createdAt.getTime() +
          faker.number.int({ min: 0, max: 7 * 24 * 60 * 60 * 1000 })
      ); // Up to 7 days later

      const orderData: OrderData = {
        userId: user.id,
        items,
        total,
        status,
        paymentMethod,
        paymentStatus,
        shippingAddress,
        billingAddress,
        createdAt,
        updatedAt,
      };

      // Add notes for some orders
      if (Math.random() > 0.7) {
        const notes = [
          "Giao hàng giờ hành chính",
          "Gọi trước khi giao",
          "Để ở bảo vệ nếu không có người",
          "Giao hàng cuối tuần",
          "Kiểm tra hàng trước khi thanh toán",
        ];
        orderData.notes = faker.helpers.arrayElement(notes);
      }

      orders.push(orderData);
    }

    return orders;
  }

  async createRecord(data: OrderData): Promise<any> {
    // Create order with transaction to ensure data consistency
    return await this.prisma.$transaction(async (tx) => {
      // Create the order
      const order = await tx.order.create({
        data: {
          userId: data.userId,
          total: data.total,
          status: data.status,
          paymentMethod: data.paymentMethod,
          paymentStatus: data.paymentStatus,
          shippingAddress: data.shippingAddress as any,
          billingAddress: data.billingAddress as any,
          notes: data.notes,
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
        },
      });

      // Create order items
      for (const item of data.items) {
        await tx.orderItem.create({
          data: {
            orderId: order.id,
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            total: item.total,
          },
        });

        // Update product stock if order is confirmed or delivered
        if (data.status === "CONFIRMED" || data.status === "DELIVERED") {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                decrement: item.quantity,
              },
            },
          });
        }
      }

      return order;
    });
  }

  validateData(data: OrderData): boolean {
    if (!data.userId) {
      this.log("User ID is required", "error");
      return false;
    }

    if (!data.items || data.items.length === 0) {
      this.log("Order must have at least one item", "error");
      return false;
    }

    if (data.total <= 0) {
      this.log("Order total must be greater than 0", "error");
      return false;
    }

    // Validate items
    for (const item of data.items) {
      if (!item.productId || item.quantity <= 0 || item.price <= 0) {
        this.log("Invalid order item data", "error");
        return false;
      }
    }

    // Validate addresses
    if (
      !data.shippingAddress.fullName ||
      !data.shippingAddress.phone ||
      !data.shippingAddress.address
    ) {
      this.log("Shipping address is incomplete", "error");
      return false;
    }

    if (
      !data.billingAddress.fullName ||
      !data.billingAddress.phone ||
      !data.billingAddress.address
    ) {
      this.log("Billing address is incomplete", "error");
      return false;
    }

    return true;
  }

  async checkExisting(where: any): Promise<boolean> {
    const existing = await this.prisma.order.findFirst({ where });
    return !!existing;
  }
}
