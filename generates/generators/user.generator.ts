import { faker } from "@faker-js/faker";
import bcrypt from "bcryptjs";
import { BaseGenerator, GeneratorConfig } from "./base/base.generator";
import { ValidationUtils } from "../utils/validation.utils";
import vietnameseNames from "../data/vietnamese-names.json";

export interface UserData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  role: "USER" | "ADMIN";
  avatar?: string;
  dateOfBirth?: Date;
  gender?: "MALE" | "FEMALE";
}

export interface UserGeneratorConfig extends GeneratorConfig {
  includeAdmin?: boolean;
  passwordLength?: number;
  includeAvatar?: boolean;
}

export class UserGenerator extends BaseGenerator<UserData> {
  private existingEmails: Set<string> = new Set();

  constructor(config: UserGeneratorConfig = {}) {
    super("User", config);
    this.config = {
      includeAdmin: false,
      passwordLength: 8,
      includeAvatar: true,
      ...config,
    };
  }

  private get userConfig(): UserGeneratorConfig {
    return this.config as UserGeneratorConfig;
  }

  private generateVietnameseName(gender?: "MALE" | "FEMALE"): string {
    const lastName = faker.helpers.arrayElement(vietnameseNames.lastNames);
    const middleName =
      Math.random() > 0.3
        ? faker.helpers.arrayElement(vietnameseNames.middleNames)
        : "";

    let firstName: string;
    if (gender === "MALE") {
      firstName = faker.helpers.arrayElement(vietnameseNames.firstNames.male);
    } else if (gender === "FEMALE") {
      firstName = faker.helpers.arrayElement(vietnameseNames.firstNames.female);
    } else {
      // Random gender
      const allFirstNames = [
        ...vietnameseNames.firstNames.male,
        ...vietnameseNames.firstNames.female,
      ];
      firstName = faker.helpers.arrayElement(allFirstNames);
    }

    return middleName
      ? `${lastName} ${middleName} ${firstName}`
      : `${lastName} ${firstName}`;
  }

  private generateUniqueEmail(name: string): string {
    const baseEmail = name
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z\s]/g, "")
      .replace(/\s+/g, ".");

    let email = `${baseEmail}@example.com`;
    let counter = 1;

    while (this.existingEmails.has(email)) {
      email = `${baseEmail}${counter}@example.com`;
      counter++;
    }

    this.existingEmails.add(email);
    return email;
  }

  private generateVietnamesePhone(): string {
    const prefixes = ["03", "05", "07", "08", "09"];
    const prefix = faker.helpers.arrayElement(prefixes);
    const number = faker.string.numeric(8);
    return `${prefix}${number}`;
  }

  private generateVietnameseAddress() {
    const provinces = [
      "TP.HCM",
      "Hà Nội",
      "Đà Nẵng",
      "Hải Phòng",
      "Cần Thơ",
      "An Giang",
      "Bà Rịa - Vũng Tàu",
      "Bắc Giang",
      "Bắc Kạn",
      "Bạc Liêu",
      "Bắc Ninh",
      "Bến Tre",
      "Bình Định",
      "Bình Dương",
      "Bình Phước",
      "Bình Thuận",
      "Cà Mau",
      "Cao Bằng",
      "Đắk Lắk",
      "Đắk Nông",
    ];

    const districts = [
      "Quận 1",
      "Quận 2",
      "Quận 3",
      "Quận 4",
      "Quận 5",
      "Quận Bình Thạnh",
      "Quận Tân Bình",
      "Quận Phú Nhuận",
      "Huyện Hóc Môn",
      "Huyện Củ Chi",
      "Huyện Bình Chánh",
    ];

    const wards = [
      "Phường 1",
      "Phường 2",
      "Phường 3",
      "Phường 4",
      "Phường 5",
      "Phường Bến Nghé",
      "Phường Đa Kao",
      "Phường Cô Giang",
      "Xã Tân Phú",
      "Xã Phước Kiển",
      "Xã Tân Nhựt",
    ];

    return {
      street: `${faker.number.int({ min: 1, max: 999 })} ${faker.helpers.arrayElement(["Đường", "Phố"])} ${faker.person.lastName()}`,
      ward: faker.helpers.arrayElement(wards),
      district: faker.helpers.arrayElement(districts),
      province: faker.helpers.arrayElement(provinces),
    };
  }

  async generateData(): Promise<UserData[]> {
    const users: UserData[] = [];

    for (let i = 0; i < (this.userConfig.count || 10); i++) {
      const gender = faker.helpers.arrayElement(["MALE", "FEMALE"]) as
        | "MALE"
        | "FEMALE";
      const name = this.generateVietnameseName(gender);
      const email = this.generateUniqueEmail(name);

      const userData: UserData = {
        name,
        email,
        password: await bcrypt.hash(
          faker.internet.password({ length: this.userConfig.passwordLength }),
          12
        ),
        phone: Math.random() > 0.2 ? this.generateVietnamesePhone() : undefined,
        // All generated users are regular users now, admins are managed separately
        gender,
        dateOfBirth: faker.date.birthdate({ min: 18, max: 65, mode: "age" }),
      };

      if (this.userConfig.includeAvatar) {
        userData.avatar = faker.image.avatar();
      }

      users.push(userData);
    }

    return users;
  }

  async createRecord(data: UserData): Promise<any> {
    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      this.log(
        `User with email ${data.email} already exists, skipping...`,
        "warning"
      );
      return existingUser;
    }

    return await this.prisma.user.create({
      data: {
        name: data.name,
        email: data.email,
        password: data.password,
        phone: data.phone,
        avatar: data.avatar,
        dateOfBirth: data.dateOfBirth,
        gender: data.gender,
      },
    });
  }

  validateData(data: UserData): boolean {
    const validation = ValidationUtils.validateUserData(data);

    if (!validation.isValid) {
      this.log(`Validation failed: ${validation.errors.join(", ")}`, "error");
      return false;
    }

    return true;
  }

  async checkExisting(where: any): Promise<boolean> {
    const existing = await this.prisma.user.findUnique({ where });
    return !!existing;
  }
}
