import { faker } from "@faker-js/faker";
import bcrypt from "bcryptjs";
import { BaseGenerator, GeneratorConfig } from "./base/base.generator";
import { ValidationUtils } from "../utils/validation.utils";
import vietnameseNames from "../data/vietnamese-names.json";

export interface UserData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  avatar?: string;
  dateOfBirth?: Date;
  gender?: "MALE" | "FEMALE";
}

export interface UserGeneratorConfig extends GeneratorConfig {
  passwordLength?: number;
  includeAvatar?: boolean;
}

export class UserGenerator extends BaseGenerator<UserData> {
  private existingEmails: Set<string> = new Set();

  constructor(config: UserGeneratorConfig = {}) {
    super("User", config);
    this.config = {
      passwordLength: 8,
      includeAvatar: true,
      ...config,
    };
  }

  private get userConfig(): UserGeneratorConfig {
    return this.config as UserGeneratorConfig;
  }

  private generateVietnameseName(gender?: "MALE" | "FEMALE"): string {
    const lastName = faker.helpers.arrayElement(vietnameseNames.lastNames);
    const middleName =
      Math.random() > 0.3
        ? faker.helpers.arrayElement(vietnameseNames.middleNames)
        : "";

    let firstName: string;
    if (gender === "MALE") {
      firstName = faker.helpers.arrayElement(vietnameseNames.firstNames.male);
    } else if (gender === "FEMALE") {
      firstName = faker.helpers.arrayElement(vietnameseNames.firstNames.female);
    } else {
      // Random gender
      const allFirstNames = [
        ...vietnameseNames.firstNames.male,
        ...vietnameseNames.firstNames.female,
      ];
      firstName = faker.helpers.arrayElement(allFirstNames);
    }

    return middleName
      ? `${lastName} ${middleName} ${firstName}`
      : `${lastName} ${firstName}`;
  }

  private generateUniqueEmail(name: string): string {
    const baseEmail = name
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z\s]/g, "")
      .replace(/\s+/g, ".");

    let email = `${baseEmail}@example.com`;
    let counter = 1;

    while (this.existingEmails.has(email)) {
      email = `${baseEmail}${counter}@example.com`;
      counter++;
    }

    this.existingEmails.add(email);
    return email;
  }

  private generateVietnamesePhone(): string {
    const prefixes = ["03", "05", "07", "08", "09"];
    const prefix = faker.helpers.arrayElement(prefixes);
    const number = faker.string.numeric(8);
    return `${prefix}${number}`;
  }

  async generateData(): Promise<UserData[]> {
    const users: UserData[] = [];

    for (let i = 0; i < (this.userConfig.count || 10); i++) {
      const gender = faker.helpers.arrayElement(["MALE", "FEMALE"]) as
        | "MALE"
        | "FEMALE";
      const name = this.generateVietnameseName(gender);
      const email = this.generateUniqueEmail(name);

      const userData: UserData = {
        name,
        email,
        password: await bcrypt.hash(
          faker.internet.password({ length: this.userConfig.passwordLength }),
          12
        ),
        phone: Math.random() > 0.2 ? this.generateVietnamesePhone() : undefined,
        // All generated users are regular users now, admins are managed separately
        gender,
        dateOfBirth: faker.date.birthdate({ min: 18, max: 65, mode: "age" }),
      };

      if (this.userConfig.includeAvatar) {
        userData.avatar = faker.image.avatar();
      }

      users.push(userData);
    }

    return users;
  }

  async createRecord(data: UserData): Promise<any> {
    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      this.log(
        `User with email ${data.email} already exists, skipping...`,
        "warning"
      );
      return existingUser;
    }

    return await this.prisma.user.create({
      data: {
        name: data.name,
        email: data.email,
        password: data.password,
        phone: data.phone,
        avatar: data.avatar,
        dateOfBirth: data.dateOfBirth,
        gender: data.gender,
      },
    });
  }

  validateData(data: UserData): boolean {
    const validation = ValidationUtils.validateUserData(data);

    if (!validation.isValid) {
      this.log(`Validation failed: ${validation.errors.join(", ")}`, "error");
      return false;
    }

    return true;
  }

  async checkExisting(where: any): Promise<boolean> {
    const existing = await this.prisma.user.findUnique({ where });
    return !!existing;
  }
}
