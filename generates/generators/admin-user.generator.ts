import { faker } from "@faker-js/faker";
import bcrypt from "bcryptjs";
import { BaseGenerator, GeneratorConfig } from "./base/base.generator";
import vietnameseNames from "../data/vietnamese-names.json";

export interface AdminUserData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  role: "ADMIN" | "MODERATOR";
  avatar?: string;
  department?: string;
  isActive: boolean;
  permissions?: Record<string, boolean>;
  createdBy?: string;
}

export interface AdminUserGeneratorConfig extends GeneratorConfig {
  passwordLength?: number;
  includeAvatar?: boolean;
  adminRatio?: number; // Ratio of ADMIN vs MODERATOR (0.0 to 1.0)
  includeDepartments?: boolean;
  createdByAdminId?: string; // ID of the admin who created these users
}

export class AdminUserGenerator extends BaseGenerator<AdminUserData> {
  private existingEmails: Set<string> = new Set();

  constructor(config: AdminUserGeneratorConfig = {}) {
    super("AdminUser", config);
    this.config = {
      passwordLength: 12, // Stronger passwords for admin users
      includeAvatar: true,
      adminRatio: 0.3, // 30% ADMIN, 70% MODERATOR by default
      includeDepartments: true,
      ...config,
    };
  }

  private get adminConfig(): AdminUserGeneratorConfig {
    return this.config as AdminUserGeneratorConfig;
  }

  private generateVietnameseName(gender?: "MALE" | "FEMALE"): string {
    const lastName = faker.helpers.arrayElement(vietnameseNames.lastNames);
    const middleName =
      Math.random() > 0.3
        ? faker.helpers.arrayElement(vietnameseNames.middleNames)
        : "";

    let firstName: string;
    if (gender === "MALE") {
      firstName = faker.helpers.arrayElement(vietnameseNames.firstNames.male);
    } else if (gender === "FEMALE") {
      firstName = faker.helpers.arrayElement(vietnameseNames.firstNames.female);
    } else {
      // Random gender
      const allFirstNames = [
        ...vietnameseNames.firstNames.male,
        ...vietnameseNames.firstNames.female,
      ];
      firstName = faker.helpers.arrayElement(allFirstNames);
    }

    return middleName
      ? `${lastName} ${middleName} ${firstName}`
      : `${lastName} ${firstName}`;
  }

  private generateUniqueEmail(name: string): string {
    const baseEmail = name
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z\s]/g, "")
      .replace(/\s+/g, ".");

    let email = `${baseEmail}@nsshop.com`; // Use company domain for admin emails
    let counter = 1;

    while (this.existingEmails.has(email)) {
      email = `${baseEmail}${counter}@nsshop.com`;
      counter++;
    }

    this.existingEmails.add(email);
    return email;
  }

  private generateVietnamesePhone(): string {
    const prefixes = ["03", "05", "07", "08", "09"];
    const prefix = faker.helpers.arrayElement(prefixes);
    const number = faker.string.numeric(8);
    return `${prefix}${number}`;
  }

  private generateDepartment(): string {
    const departments = [
      "IT",
      "Marketing",
      "Sales",
      "Customer Service",
      "Operations",
      "Finance",
      "HR",
      "Product Management",
      "Quality Assurance",
      "Logistics",
    ];
    return faker.helpers.arrayElement(departments);
  }

  private generateModeratorPermissions(): Record<string, boolean> {
    // Generate realistic permission combinations for moderators
    const permissionSets = [
      // Product Manager
      {
        manage_products: true,
        manage_categories: true,
        view_analytics: true,
        manage_orders: false,
        manage_users: false,
      },
      // Order Manager
      {
        manage_products: false,
        manage_categories: false,
        view_analytics: true,
        manage_orders: true,
        manage_users: false,
      },
      // Customer Service
      {
        manage_products: false,
        manage_categories: false,
        view_analytics: false,
        manage_orders: true,
        manage_users: true,
      },
      // Full Moderator
      {
        manage_products: true,
        manage_categories: true,
        view_analytics: true,
        manage_orders: true,
        manage_users: true,
      },
    ];

    return faker.helpers.arrayElement(permissionSets);
  }

  async generateData(): Promise<AdminUserData[]> {
    const adminUsers: AdminUserData[] = [];

    for (let i = 0; i < (this.adminConfig.count || 5); i++) {
      const name = this.generateVietnameseName();
      const email = this.generateUniqueEmail(name);
      const role = Math.random() < (this.adminConfig.adminRatio || 0.3) ? "ADMIN" : "MODERATOR";

      const adminUserData: AdminUserData = {
        name,
        email,
        password: await bcrypt.hash(
          faker.internet.password({ 
            length: this.adminConfig.passwordLength,
            memorable: false,
            pattern: /[A-Za-z0-9!@#$%^&*]/
          }),
          12
        ),
        phone: Math.random() > 0.3 ? this.generateVietnamesePhone() : undefined,
        role,
        isActive: Math.random() > 0.1, // 90% active, 10% inactive
        createdBy: this.adminConfig.createdByAdminId,
      };

      if (this.adminConfig.includeAvatar) {
        adminUserData.avatar = faker.image.avatar();
      }

      if (this.adminConfig.includeDepartments) {
        adminUserData.department = this.generateDepartment();
      }

      // Only moderators have specific permissions, admins have full access
      if (role === "MODERATOR") {
        adminUserData.permissions = this.generateModeratorPermissions();
      }

      adminUsers.push(adminUserData);
    }

    return adminUsers;
  }

  async createRecord(data: AdminUserData): Promise<any> {
    // Check if admin user already exists
    const existingAdmin = await this.prisma.adminUser.findUnique({
      where: { email: data.email },
    });

    if (existingAdmin) {
      this.log(
        `Admin user with email ${data.email} already exists, skipping...`,
        "warning"
      );
      return existingAdmin;
    }

    return await this.prisma.adminUser.create({
      data: {
        name: data.name,
        email: data.email,
        password: data.password,
        phone: data.phone,
        role: data.role,
        avatar: data.avatar,
        department: data.department,
        isActive: data.isActive,
        permissions: data.permissions,
        createdBy: data.createdBy,
      },
    });
  }

  validateData(data: AdminUserData): boolean {
    const errors: string[] = [];

    // Validate name
    if (!data.name || data.name.length < 2 || data.name.length > 100) {
      errors.push("Name must be between 2 and 100 characters");
    }

    // Validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!data.email || !emailRegex.test(data.email)) {
      errors.push("Invalid email format");
    }

    // Validate role
    if (!["ADMIN", "MODERATOR"].includes(data.role)) {
      errors.push("Role must be ADMIN or MODERATOR");
    }

    // Validate phone (optional)
    if (data.phone) {
      const phoneRegex = /^(03|05|07|08|09)\d{8}$/;
      if (!phoneRegex.test(data.phone)) {
        errors.push("Invalid Vietnamese phone number format");
      }
    }

    if (errors.length > 0) {
      this.log(`Validation failed: ${errors.join(", ")}`, "error");
      return false;
    }

    return true;
  }

  async checkExisting(where: any): Promise<boolean> {
    const existing = await this.prisma.adminUser.findUnique({ where });
    return !!existing;
  }

  // Helper method to generate a specific admin user
  async generateSpecificAdmin(
    name: string,
    email: string,
    role: "ADMIN" | "MODERATOR",
    department?: string
  ): Promise<AdminUserData> {
    return {
      name,
      email,
      password: await bcrypt.hash(
        faker.internet.password({ 
          length: this.adminConfig.passwordLength,
          memorable: false,
          pattern: /[A-Za-z0-9!@#$%^&*]/
        }),
        12
      ),
      phone: this.generateVietnamesePhone(),
      role,
      avatar: this.adminConfig.includeAvatar ? faker.image.avatar() : undefined,
      department: department || (this.adminConfig.includeDepartments ? this.generateDepartment() : undefined),
      isActive: true,
      permissions: role === "MODERATOR" ? this.generateModeratorPermissions() : undefined,
      createdBy: this.adminConfig.createdByAdminId,
    };
  }
}
