# Migration từ Scripts cũ sang Generators mới

## 📋 Tóm tắt thay đổi

Đã tái cấu trúc hoàn toàn hệ thống tạo dữ liệu mẫu từ scripts đơn gi<PERSON>n sang hệ thống generators chuyên nghiệp.

## 🔄 So sánh Scripts cũ vs Generators mới

### Scripts cũ (trong `/scripts`)
```
scripts/
├── generate-sample-data.ts  # Hardcoded data arrays
├── create-demo-data.md      # Documentation
└── validate-data.ts         # Validation script

prisma/
├── seed.ts                  # Basic seeding
├── seed-products.ts         # Product seeding
└── seed-settings.ts         # Settings seeding
```

**Vấn đề:**
- Dữ liệu hardcoded, không linh hoạt
- Không có thư viện chuyên dụng
- Code không được tổ chức tốt
- Khó maintain và extend
- Không có CLI interface
- Không có configuration system

### Generators mới (trong `/generates`)
```
generates/
├── generators/              # Modular generators
│   ├── base/               # Base class
│   ├── user.generator.ts
│   ├── product.generator.ts
│   ├── category.generator.ts
│   ├── order.generator.ts
│   └── review.generator.ts
├── templates/              # Data templates
├── data/                   # Static data files
├── utils/                  # Utility functions
├── config/                 # Configuration
├── cli/                    # CLI interface
└── index.ts               # Main orchestrator
```

**Cải thiện:**
- ✅ Sử dụng Faker.js cho dữ liệu realistic
- ✅ Cấu trúc modular, dễ maintain
- ✅ CLI interface linh hoạt
- ✅ Configuration system hoàn chỉnh
- ✅ Validation tự động
- ✅ Transaction support
- ✅ Verbose logging
- ✅ TypeScript types đầy đủ

## 🚀 Cách migration

### 1. Scripts mới (khuyến nghị)
```bash
# Thay vì
npm run db:generate-data

# Sử dụng
npm run generate
npm run generate:interactive
```

### 2. Scripts cũ vẫn hoạt động
```bash
# Vẫn có thể sử dụng
npm run db:generate-data
npm run db:full-setup
```

### 3. Setup database hoàn chỉnh
```bash
# Mới (khuyến nghị)
npm run db:setup-new

# Cũ (vẫn hoạt động)
npm run db:full-setup
```

## 📊 So sánh dữ liệu được tạo

### Scripts cũ
- ~30 sản phẩm hardcoded
- 5 users cố định
- 6 đơn hàng mẫu
- Reviews với nội dung cố định
- Categories cố định

### Generators mới
- **Users**: Tên Việt thực tế, địa chỉ chi tiết, avatar
- **Products**: Tên realistic, mô tả đa dạng, variants, tags
- **Reviews**: Nội dung Việt đa dạng, rating distribution thực tế
- **Orders**: Logic phức tạp, địa chỉ Việt, payment methods realistic
- **Categories**: Cấu trúc phân cấp hoàn chỉnh

## ⚙️ Configuration

### Scripts cũ
- Không có configuration
- Phải sửa code để thay đổi

### Generators mới
```typescript
// Có thể tùy chỉnh mọi thứ
const config = {
  users: { count: 50, includeAdmin: true },
  products: { count: 100, salePercentage: 25 },
  reviews: { count: 200, verifiedPercentage: 80 },
  orders: { count: 50 },
  global: { verbose: true, seed: 12345 }
};
```

## 🧪 Testing

### Scripts cũ
- Không có test system
- Khó debug

### Generators mới
```bash
# Test với dữ liệu nhỏ
npm run generate:test

# Test với seed cố định
npm run generate -- --seed 12345

# Test từng generator riêng
npm run generate:users
npm run generate:products
```

## 📈 Performance

### Scripts cũ
- Tạo tuần tự, chậm
- Không có transaction
- Có thể bị lỗi giữa chừng

### Generators mới
- Transaction support
- Parallel processing (khi có thể)
- Error handling tốt hơn
- Verbose logging để debug

## 🔧 Customization

### Scripts cũ
```typescript
// Phải sửa trực tiếp trong code
const fashionProducts = [
  {
    name: "Áo Kiểu Nữ Tay Dài Vintage",
    // ...
  }
];
```

### Generators mới
```typescript
// Sửa trong data files
// generates/data/product-names.json
// generates/templates/reviews/review-templates.json
// generates/data/vietnamese-names.json

// Hoặc extend generators
class CustomProductGenerator extends ProductGenerator {
  // Custom logic
}
```

## 🚨 Breaking Changes

### Scripts bị thay thế
- `scripts/generate-sample-data.ts` → `generates/generators/`
- Hardcoded data → Dynamic data với Faker.js

### Scripts vẫn tương thích
- `prisma/seed.ts` → Vẫn hoạt động bình thường
- `npm run db:seed` → Không thay đổi
- `npm run db:generate-data` → Vẫn hoạt động

### Scripts mới
- `npm run generate` → Khuyến nghị sử dụng
- `npm run generate:interactive` → Chế độ interactive
- `npm run generate:test` → Test generators

## 📝 Recommendations

### Cho Development
```bash
# Sử dụng generators mới
npm run generate:interactive

# Hoặc với config tùy chỉnh
npm run generate -- --users 20 --products 50 --verbose
```

### Cho Testing
```bash
# Test với dữ liệu nhỏ
npm run generate:test

# Test với seed cố định
npm run generate -- --seed 12345 --users 5 --products 10
```

### Cho Production
```bash
# Chỉ seed dữ liệu cơ bản
npm run db:seed

# KHÔNG chạy generators trên production
```

## 🔮 Future Plans

1. **Thêm generators mới**: Settings, Coupons, Notifications
2. **Improve performance**: Batch operations, parallel processing
3. **Advanced CLI**: More interactive options, progress bars
4. **Data relationships**: Smarter relationship generation
5. **Export/Import**: Backup và restore generated data

## 📞 Support

Nếu có vấn đề với generators mới:

1. Kiểm tra documentation: `generates/README.md`
2. Chạy test: `npm run generate:test`
3. Sử dụng verbose mode: `npm run generate -- --verbose`
4. Fallback về scripts cũ: `npm run db:generate-data`

## ✅ Checklist Migration

- [x] Tạo cấu trúc thư mục `/generates`
- [x] Cài đặt thư viện chuyên dụng (Faker.js, Commander, etc.)
- [x] Tạo BaseGenerator class
- [x] Tạo UserGenerator với dữ liệu Việt realistic
- [x] Tạo CategoryGenerator với cấu trúc phân cấp
- [x] Tạo ProductGenerator với variants và tags
- [x] Tạo ReviewGenerator với templates Việt
- [x] Tạo OrderGenerator với logic phức tạp
- [x] Tạo main orchestrator
- [x] Tạo CLI interface
- [x] Cập nhật package.json scripts
- [x] Tạo configuration system
- [x] Viết documentation đầy đủ
- [x] Tạo test script
- [x] Đảm bảo backward compatibility

🎉 **Migration hoàn thành!** Hệ thống generators mới đã sẵn sàng sử dụng.
