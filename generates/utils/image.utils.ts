/**
 * Utility functions for generating image URLs
 */

export interface ImageOptions {
  width?: number;
  height?: number;
  category?: string;
  keywords?: string[];
}

export class ImageGenerator {
  private static readonly UNSPLASH_BASE = 'https://images.unsplash.com';
  private static readonly PICSUM_BASE = 'https://picsum.photos';

  /**
   * Generate Unsplash image URL with specific parameters
   */
  static generateUnsplashUrl(options: ImageOptions = {}): string {
    const { width = 500, height = 600, category, keywords = [] } = options;
    
    let url = `${this.UNSPLASH_BASE}/photo-`;
    
    // Add random photo ID (simplified approach)
    const photoIds = [
      '1594633312681-425c7b97ccd1',
      '1551698618-1dfe5d97d256',
      '1515372039744-b8f02a3ae446',
      '1544966503-7ad5ac882d5d',
      '1506629905252-70d1e7d50e8a',
      '1525507119028-ed4c629a60a3',
      '1560243563-062bcc69589a',
      '1578662996442-374dcbcf3b3f',
      '1596755094514-f87e34085b2c',
      '1581044777550-4cfa60707c03'
    ];
    
    const randomId = photoIds[Math.floor(Math.random() * photoIds.length)];
    url += `${randomId}?w=${width}&h=${height}`;
    
    if (category) {
      url += `&q=${encodeURIComponent(category)}`;
    }
    
    if (keywords.length > 0) {
      url += `&keywords=${encodeURIComponent(keywords.join(','))}`;
    }
    
    return url;
  }

  /**
   * Generate Picsum (Lorem Picsum) image URL
   */
  static generatePicsumUrl(options: ImageOptions = {}): string {
    const { width = 500, height = 600 } = options;
    const randomId = Math.floor(Math.random() * 1000) + 1;
    return `${this.PICSUM_BASE}/${width}/${height}?random=${randomId}`;
  }

  /**
   * Generate multiple image URLs for a product
   */
  static generateProductImages(count: number = 3, options: ImageOptions = {}): string[] {
    const images: string[] = [];
    
    for (let i = 0; i < count; i++) {
      // Mix between Unsplash and Picsum
      if (Math.random() > 0.5) {
        images.push(this.generateUnsplashUrl(options));
      } else {
        images.push(this.generatePicsumUrl(options));
      }
    }
    
    return images;
  }

  /**
   * Generate fashion-specific image URLs
   */
  static generateFashionImages(category: string, count: number = 2): string[] {
    const fashionKeywords: Record<string, string[]> = {
      'ao-nu': ['women fashion', 'blouse', 'shirt', 'top'],
      'quan-nu': ['women pants', 'trousers', 'jeans', 'skirt'],
      'vay-dam': ['dress', 'women dress', 'fashion dress'],
      'giay-nu': ['women shoes', 'heels', 'boots', 'sneakers'],
      'phu-kien': ['accessories', 'jewelry', 'bag', 'handbag'],
      'ao-nam': ['men fashion', 'men shirt', 'polo', 'tshirt'],
      'quan-nam': ['men pants', 'jeans', 'trousers', 'shorts'],
      'giay-nam': ['men shoes', 'sneakers', 'boots', 'formal shoes']
    };

    const keywords = fashionKeywords[category] || ['fashion', 'clothing'];
    
    return this.generateProductImages(count, {
      category: 'fashion',
      keywords,
      width: 500,
      height: 600
    });
  }
}
