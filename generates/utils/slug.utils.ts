/**
 * Utility functions for generating slugs
 */

export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

export function generateUniqueSlug(text: string, existingSlugs: string[]): string {
  let baseSlug = generateSlug(text);
  let slug = baseSlug;
  let counter = 1;

  while (existingSlugs.includes(slug)) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}

export function generateSKU(prefix: string, length: number = 6): string {
  const randomPart = Math.random()
    .toString(36)
    .substr(2, length)
    .toUpperCase();
  return `${prefix}-${randomPart}`;
}

export function generateUniqueSKU(prefix: string, existingSKUs: string[], length: number = 6): string {
  let sku: string;
  let attempts = 0;
  const maxAttempts = 100;

  do {
    sku = generateSKU(prefix, length);
    attempts++;
  } while (existingSKUs.includes(sku) && attempts < maxAttempts);

  if (attempts >= maxAttempts) {
    // Fallback with timestamp
    sku = `${prefix}-${Date.now().toString(36).toUpperCase()}`;
  }

  return sku;
}
