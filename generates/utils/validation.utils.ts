/**
 * Validation utility functions
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export class ValidationUtils {
  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate Vietnamese phone number
   */
  static validateVietnamesePhone(phone: string): boolean {
    const phoneRegex = /^(0|\+84)[3|5|7|8|9][0-9]{8}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Validate price (must be positive number)
   */
  static validatePrice(price: number): boolean {
    return typeof price === 'number' && price > 0 && isFinite(price);
  }

  /**
   * Validate stock quantity
   */
  static validateStock(stock: number): boolean {
    return typeof stock === 'number' && stock >= 0 && Number.isInteger(stock);
  }

  /**
   * Validate slug format
   */
  static validateSlug(slug: string): boolean {
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
    return slugRegex.test(slug) && slug.length > 0 && slug.length <= 100;
  }

  /**
   * Validate SKU format
   */
  static validateSKU(sku: string): boolean {
    const skuRegex = /^[A-Z0-9-]+$/;
    return skuRegex.test(sku) && sku.length >= 3 && sku.length <= 20;
  }

  /**
   * Validate required string field
   */
  static validateRequiredString(value: string, minLength: number = 1, maxLength: number = 255): ValidationResult {
    const errors: string[] = [];

    if (!value || typeof value !== 'string') {
      errors.push('Field is required and must be a string');
    } else {
      if (value.trim().length < minLength) {
        errors.push(`Field must be at least ${minLength} characters long`);
      }
      if (value.length > maxLength) {
        errors.push(`Field must not exceed ${maxLength} characters`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate array field
   */
  static validateArray<T>(value: T[], minLength: number = 0, maxLength: number = 100): ValidationResult {
    const errors: string[] = [];

    if (!Array.isArray(value)) {
      errors.push('Field must be an array');
    } else {
      if (value.length < minLength) {
        errors.push(`Array must have at least ${minLength} items`);
      }
      if (value.length > maxLength) {
        errors.push(`Array must not have more than ${maxLength} items`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate user data
   */
  static validateUserData(data: any): ValidationResult {
    const errors: string[] = [];

    // Validate name
    const nameValidation = this.validateRequiredString(data.name, 2, 100);
    if (!nameValidation.isValid) {
      errors.push(...nameValidation.errors.map(e => `Name: ${e}`));
    }

    // Validate email
    if (!data.email || !this.validateEmail(data.email)) {
      errors.push('Email: Invalid email format');
    }

    // Validate phone (optional)
    if (data.phone && !this.validateVietnamesePhone(data.phone)) {
      errors.push('Phone: Invalid Vietnamese phone number format');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate product data
   */
  static validateProductData(data: any): ValidationResult {
    const errors: string[] = [];

    // Validate name
    const nameValidation = this.validateRequiredString(data.name, 3, 200);
    if (!nameValidation.isValid) {
      errors.push(...nameValidation.errors.map(e => `Name: ${e}`));
    }

    // Validate slug
    if (!data.slug || !this.validateSlug(data.slug)) {
      errors.push('Slug: Invalid slug format');
    }

    // Validate price
    if (!this.validatePrice(data.price)) {
      errors.push('Price: Must be a positive number');
    }

    // Validate sale price (optional)
    if (data.salePrice !== undefined && data.salePrice !== null) {
      if (!this.validatePrice(data.salePrice)) {
        errors.push('Sale Price: Must be a positive number');
      } else if (data.salePrice >= data.price) {
        errors.push('Sale Price: Must be less than regular price');
      }
    }

    // Validate stock
    if (!this.validateStock(data.stock)) {
      errors.push('Stock: Must be a non-negative integer');
    }

    // Validate SKU
    if (!data.sku || !this.validateSKU(data.sku)) {
      errors.push('SKU: Invalid SKU format');
    }

    // Validate images
    const imagesValidation = this.validateArray(data.images, 1, 10);
    if (!imagesValidation.isValid) {
      errors.push(...imagesValidation.errors.map(e => `Images: ${e}`));
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
