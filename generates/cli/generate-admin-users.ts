#!/usr/bin/env tsx

import chalk from "chalk";
import { AdminUserGenerator } from "../generators/admin-user.generator";

interface AdminGenerationOptions {
  count?: number;
  adminRatio?: number;
  department?: string;
  createdBy?: string;
  verbose?: boolean;
}

async function generateAdminUsers(options: AdminGenerationOptions = {}) {
  const {
    count = 5,
    adminRatio = 0.4,
    department,
    createdBy,
    verbose = true,
  } = options;

  console.log(chalk.blue("🚀 Starting admin user generation..."));
  console.log(chalk.gray(`Configuration:`));
  console.log(chalk.gray(`  - Count: ${count}`));
  console.log(chalk.gray(`  - Admin Ratio: ${adminRatio * 100}%`));
  console.log(chalk.gray(`  - Department: ${department || "Random"}`));
  console.log(chalk.gray(`  - Created By: ${createdBy || "None"}`));
  console.log("");

  try {
    const generator = new AdminUserGenerator({
      count,
      adminRatio,
      includeAvatar: true,
      includeDepartments: !department, // If specific department provided, don't randomize
      createdByAdminId: createdBy,
      verbose,
    });

    // If specific department provided, override the generator
    if (department) {
      const originalGenerateData = generator.generateData.bind(generator);
      generator.generateData = async function() {
        const data = await originalGenerateData();
        return data.map(admin => ({ ...admin, department }));
      };
    }

    const result = await generator.generate();

    if (result.success) {
      console.log(chalk.green(`✅ Successfully generated ${result.count} admin users!`));
      console.log(chalk.gray(`Duration: ${result.duration}ms`));
      
      if (verbose && result.data) {
        console.log(chalk.blue("\n📋 Generated Admin Users:"));
        result.data.forEach((admin: any, index: number) => {
          console.log(chalk.white(`${index + 1}. ${admin.name}`));
          console.log(chalk.gray(`   Email: ${admin.email}`));
          console.log(chalk.gray(`   Role: ${admin.role}`));
          console.log(chalk.gray(`   Department: ${admin.department || "N/A"}`));
          console.log(chalk.gray(`   Active: ${admin.isActive ? "Yes" : "No"}`));
          if (admin.permissions) {
            const permissions = Object.entries(admin.permissions)
              .filter(([_, value]) => value)
              .map(([key, _]) => key)
              .join(", ");
            console.log(chalk.gray(`   Permissions: ${permissions}`));
          }
          console.log("");
        });
      }
    } else {
      console.log(chalk.red(`❌ Admin user generation failed: ${result.error}`));
      process.exit(1);
    }

    await generator.cleanup();
  } catch (error) {
    console.log(chalk.red(`❌ Unexpected error: ${error}`));
    process.exit(1);
  }
}

// CLI argument parsing
function parseArgs(): AdminGenerationOptions {
  const args = process.argv.slice(2);
  const options: AdminGenerationOptions = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    const nextArg = args[i + 1];

    switch (arg) {
      case "--count":
      case "-c":
        if (nextArg && !isNaN(Number(nextArg))) {
          options.count = Number(nextArg);
          i++;
        }
        break;
      case "--admin-ratio":
      case "-r":
        if (nextArg && !isNaN(Number(nextArg))) {
          const ratio = Number(nextArg);
          if (ratio >= 0 && ratio <= 1) {
            options.adminRatio = ratio;
          }
          i++;
        }
        break;
      case "--department":
      case "-d":
        if (nextArg) {
          options.department = nextArg;
          i++;
        }
        break;
      case "--created-by":
      case "-b":
        if (nextArg) {
          options.createdBy = nextArg;
          i++;
        }
        break;
      case "--quiet":
      case "-q":
        options.verbose = false;
        break;
      case "--help":
      case "-h":
        showHelp();
        process.exit(0);
        break;
    }
  }

  return options;
}

function showHelp() {
  console.log(chalk.blue("Admin User Generator"));
  console.log("");
  console.log("Usage: tsx generate-admin-users.ts [options]");
  console.log("");
  console.log("Options:");
  console.log("  -c, --count <number>        Number of admin users to generate (default: 5)");
  console.log("  -r, --admin-ratio <ratio>   Ratio of ADMIN vs MODERATOR (0.0-1.0, default: 0.4)");
  console.log("  -d, --department <dept>     Specific department for all users");
  console.log("  -b, --created-by <id>       ID of admin who created these users");
  console.log("  -q, --quiet                 Suppress verbose output");
  console.log("  -h, --help                  Show this help message");
  console.log("");
  console.log("Examples:");
  console.log("  tsx generate-admin-users.ts --count 10");
  console.log("  tsx generate-admin-users.ts --count 5 --admin-ratio 0.6");
  console.log("  tsx generate-admin-users.ts --department IT --count 3");
  console.log("  tsx generate-admin-users.ts --created-by admin-id-123 --quiet");
}

// Main execution
if (require.main === module) {
  const options = parseArgs();
  generateAdminUsers(options).catch((error) => {
    console.error(chalk.red("Fatal error:"), error);
    process.exit(1);
  });
}

export { generateAdminUsers };
