import chalk from "chalk";
import { PrismaClient } from "@prisma/client";
import { UserGenerator } from "./generators/user.generator";
import { AdminUserGenerator } from "./generators/admin-user.generator";
import { CategoryGenerator } from "./generators/category.generator";
import { ProductGenerator } from "./generators/product.generator";
import { ReviewGenerator } from "./generators/review.generator";
import { OrderGenerator } from "./generators/order.generator";
import { defaultConfig, GeneratorConfiguration } from "./config/default.config";

export interface GenerationResult {
  success: boolean;
  results: {
    users?: any;
    adminUsers?: any;
    categories?: any;
    products?: any;
    reviews?: any;
    orders?: any;
  };
  errors: string[];
  totalDuration: number;
}

export class DataGenerator {
  private prisma: PrismaClient;
  private config: GeneratorConfiguration;

  constructor(config: Partial<GeneratorConfiguration> = {}) {
    this.prisma = new PrismaClient();
    this.config = this.mergeConfig(defaultConfig, config);
  }

  private mergeConfig(
    defaultConfig: GeneratorConfiguration,
    userConfig: Partial<GeneratorConfiguration>
  ): GeneratorConfiguration {
    return {
      users: { ...defaultConfig.users, ...userConfig.users },
      adminUsers: { ...defaultConfig.adminUsers, ...userConfig.adminUsers },
      categories: { ...defaultConfig.categories, ...userConfig.categories },
      products: { ...defaultConfig.products, ...userConfig.products },
      reviews: { ...defaultConfig.reviews, ...userConfig.reviews },
      orders: { ...defaultConfig.orders, ...userConfig.orders },
      global: { ...defaultConfig.global, ...userConfig.global },
    };
  }

  private log(
    message: string,
    type: "info" | "success" | "error" | "warning" = "info"
  ): void {
    if (!this.config.global.verbose) return;

    const colors = {
      info: chalk.blue,
      success: chalk.green,
      error: chalk.red,
      warning: chalk.yellow,
    };

    const icons = {
      info: "ℹ",
      success: "✅",
      error: "❌",
      warning: "⚠️",
    };

    console.log(colors[type](`${icons[type]} ${message}`));
  }

  async generateAll(): Promise<GenerationResult> {
    const startTime = Date.now();
    const results: GenerationResult["results"] = {};
    const errors: string[] = [];

    this.log("🚀 Starting data generation...", "info");
    this.log(`Configuration: ${JSON.stringify(this.config, null, 2)}`, "info");

    try {
      // Step 1: Generate Users (regular customers only)
      this.log("👥 Generating users...", "info");
      const userGenerator = new UserGenerator({
        count: this.config.users.count,
        includeAvatar: this.config.users.includeAvatar,
        locale: this.config.global.locale,
        verbose: this.config.global.verbose,
        seed: this.config.global.seed,
      });

      results.users = await userGenerator.generate();
      await userGenerator.cleanup();

      if (!results.users.success) {
        errors.push(`User generation failed: ${results.users.error}`);
        this.log(`User generation failed: ${results.users.error}`, "error");
      } else {
        this.log(
          `Generated ${results.users.count} users in ${results.users.duration}ms`,
          "success"
        );
      }

      // Step 1.5: Generate Admin Users (separate from regular users)
      this.log("👨‍💼 Generating admin users...", "info");
      const adminUserGenerator = new AdminUserGenerator({
        count: this.config.adminUsers?.count || 3,
        adminRatio: this.config.adminUsers?.adminRatio || 0.3,
        includeAvatar: this.config.adminUsers?.includeAvatar !== false,
        includeDepartments:
          this.config.adminUsers?.includeDepartments !== false,
        locale: this.config.global.locale,
        verbose: this.config.global.verbose,
        seed: this.config.global.seed,
      });

      results.adminUsers = await adminUserGenerator.generate();
      await adminUserGenerator.cleanup();

      if (!results.adminUsers.success) {
        errors.push(
          `Admin user generation failed: ${results.adminUsers.error}`
        );
        this.log(
          `Admin user generation failed: ${results.adminUsers.error}`,
          "error"
        );
      } else {
        this.log(
          `Generated ${results.adminUsers.count} admin users in ${results.adminUsers.duration}ms`,
          "success"
        );
      }

      // Step 2: Generate Categories
      this.log("📂 Generating categories...", "info");
      const categoryGenerator = new CategoryGenerator({
        count: this.config.categories.count,
        useDefaultCategories: this.config.categories.useDefaultCategories,
        includeImages: this.config.categories.includeImages,
        locale: this.config.global.locale,
        verbose: this.config.global.verbose,
        seed: this.config.global.seed,
      });

      results.categories = await categoryGenerator.generate();
      await categoryGenerator.cleanup();

      if (!results.categories.success) {
        errors.push(`Category generation failed: ${results.categories.error}`);
        this.log(
          `Category generation failed: ${results.categories.error}`,
          "error"
        );
      } else {
        this.log(
          `Generated ${results.categories.count} categories in ${results.categories.duration}ms`,
          "success"
        );
      }

      // Step 3: Generate Products (requires categories)
      if (results.categories.success) {
        this.log("🛍️ Generating products...", "info");
        const productGenerator = new ProductGenerator({
          count: this.config.products.count,
          includeVariants: this.config.products.includeVariants,
          includeDimensions: this.config.products.includeDimensions,
          salePercentage: this.config.products.salePercentage,
          featuredPercentage: this.config.products.featuredPercentage,
          minPrice: this.config.products.minPrice,
          maxPrice: this.config.products.maxPrice,
          minStock: this.config.products.minStock,
          maxStock: this.config.products.maxStock,
          locale: this.config.global.locale,
          verbose: this.config.global.verbose,
          seed: this.config.global.seed,
        });

        results.products = await productGenerator.generate();
        await productGenerator.cleanup();

        if (!results.products.success) {
          errors.push(`Product generation failed: ${results.products.error}`);
          this.log(
            `Product generation failed: ${results.products.error}`,
            "error"
          );
        } else {
          this.log(
            `Generated ${results.products.count} products in ${results.products.duration}ms`,
            "success"
          );
        }
      } else {
        errors.push(
          "Skipping product generation due to category generation failure"
        );
        this.log(
          "Skipping product generation due to category generation failure",
          "warning"
        );
      }

      // Step 4: Generate Reviews (requires users and products)
      if (results.users.success && results.products?.success) {
        this.log("⭐ Generating reviews...", "info");
        const reviewGenerator = new ReviewGenerator({
          count: this.config.reviews.count,
          verifiedPercentage: this.config.reviews.verifiedPercentage,
          includeHelpfulCount: this.config.reviews.includeHelpfulCount,
          ratingDistribution: this.config.reviews.ratingDistribution,
          locale: this.config.global.locale,
          verbose: this.config.global.verbose,
          seed: this.config.global.seed,
        });

        results.reviews = await reviewGenerator.generate();
        await reviewGenerator.cleanup();

        if (!results.reviews.success) {
          errors.push(`Review generation failed: ${results.reviews.error}`);
          this.log(
            `Review generation failed: ${results.reviews.error}`,
            "error"
          );
        } else {
          this.log(
            `Generated ${results.reviews.count} reviews in ${results.reviews.duration}ms`,
            "success"
          );
        }
      } else {
        errors.push(
          "Skipping review generation due to missing users or products"
        );
        this.log(
          "Skipping review generation due to missing users or products",
          "warning"
        );
      }

      // Step 5: Generate Orders (requires users and products)
      if (results.users.success && results.products?.success) {
        this.log("🛒 Generating orders...", "info");
        const orderGenerator = new OrderGenerator({
          count: this.config.orders.count,
          minItems: this.config.orders.minItems,
          maxItems: this.config.orders.maxItems,
          statusDistribution: this.config.orders.statusDistribution,
          paymentMethodDistribution:
            this.config.orders.paymentMethodDistribution,
          locale: this.config.global.locale,
          verbose: this.config.global.verbose,
          seed: this.config.global.seed,
        });

        results.orders = await orderGenerator.generate();
        await orderGenerator.cleanup();

        if (!results.orders.success) {
          errors.push(`Order generation failed: ${results.orders.error}`);
          this.log(`Order generation failed: ${results.orders.error}`, "error");
        } else {
          this.log(
            `Generated ${results.orders.count} orders in ${results.orders.duration}ms`,
            "success"
          );
        }
      } else {
        errors.push(
          "Skipping order generation due to missing users or products"
        );
        this.log(
          "Skipping order generation due to missing users or products",
          "warning"
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      errors.push(`Unexpected error: ${errorMessage}`);
      this.log(`Unexpected error: ${errorMessage}`, "error");
    } finally {
      await this.prisma.$disconnect();
    }

    const totalDuration = Date.now() - startTime;
    const success = errors.length === 0;

    if (success) {
      this.log(
        `🎉 Data generation completed successfully in ${totalDuration}ms`,
        "success"
      );
    } else {
      this.log(
        `⚠️ Data generation completed with ${errors.length} errors in ${totalDuration}ms`,
        "warning"
      );
    }

    return {
      success,
      results,
      errors,
      totalDuration,
    };
  }

  async cleanup(): Promise<void> {
    await this.prisma.$disconnect();
  }
}

// Export for direct usage
export * from "./generators/user.generator";
export * from "./generators/admin-user.generator";
export * from "./generators/category.generator";
export * from "./generators/product.generator";
export * from "./generators/review.generator";
export * from "./generators/order.generator";
export * from "./config/default.config";
