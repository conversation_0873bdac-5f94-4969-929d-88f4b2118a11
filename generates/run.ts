#!/usr/bin/env tsx

import { DataGenerator } from "./index";

async function main() {
  console.log("🚀 Starting NS Shop data generation...");

  const generator = new DataGenerator({
    users: {
      count: 15,
      includeAdmin: true,
      includeAddress: true,
      includeAvatar: true,
    },
    products: {
      count: 30,
      includeVariants: false,
      includeDimensions: false,
      salePercentage: 25,
      featuredPercentage: 15,
      minPrice: 50000,
      maxPrice: 2000000,
      minStock: 0,
      maxStock: 100,
    },
    reviews: {
      count: 60,
      verifiedPercentage: 70,
      includeHelpfulCount: true,
      ratingDistribution: {
        1: 5,
        2: 10,
        3: 15,
        4: 30,
        5: 40,
      },
    },
    orders: {
      count: 20,
      minItems: 1,
      maxItems: 5,
      statusDistribution: {
        PENDING: 20,
        CONFIRMED: 15,
        PROCESSING: 15,
        SHIPPED: 20,
        DELIVERED: 25,
        CANCELLED: 5,
      },
      paymentMethodDistribution: {
        COD: 60,
        BANK_TRANSFER: 25,
        CREDIT_CARD: 15,
      },
    },
    global: {
      locale: "vi",
      verbose: true,
    },
  });

  try {
    const result = await generator.generateAll();

    if (result.success) {
      console.log("✅ Data generation completed successfully!");
    } else {
      console.log("⚠️ Data generation completed with errors:");
      result.errors.forEach((error) => console.log(`  - ${error}`));
    }
  } catch (error) {
    console.error("❌ Data generation failed:", error);
    process.exit(1);
  } finally {
    await generator.cleanup();
  }
}

if (require.main === module) {
  main();
}
