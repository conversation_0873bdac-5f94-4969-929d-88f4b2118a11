#!/usr/bin/env tsx

/**
 * Test script để kiểm tra generators hoạt động đúng
 */

import { DataGenerator } from "./index";

async function testGenerators() {
  console.log("🧪 Testing NS Shop Data Generators...");

  const generator = new DataGenerator({
    users: {
      count: 3,
      includeAdmin: true,
      includeAddress: true,
      includeAvatar: false, // Tắt avatar để test nhanh hơn
    },
    categories: {
      count: 0,
      useDefaultCategories: true,
      includeImages: false,
    },
    products: {
      count: 5,
      includeVariants: true,
      includeDimensions: false,
      salePercentage: 50,
      featuredPercentage: 40,
      minPrice: 50000,
      maxPrice: 500000,
      minStock: 0,
      maxStock: 50,
    },
    reviews: {
      count: 8,
      verifiedPercentage: 60,
      includeHelpfulCount: true,
      ratingDistribution: {
        1: 5,
        2: 10,
        3: 15,
        4: 30,
        5: 40,
      },
    },
    orders: {
      count: 3,
      minItems: 1,
      maxItems: 3,
      statusDistribution: {
        PENDING: 30,
        CONFIRMED: 20,
        PROCESSING: 20,
        SHIPPED: 15,
        DELIVERED: 10,
        CANCELLED: 5,
      },
      paymentMethodDistribution: {
        COD: 70,
        BANK_TRANSFER: 20,
        CREDIT_CARD: 10,
      },
    },
    global: {
      locale: "vi",
      verbose: true,
      seed: 12345, // Fixed seed for consistent testing
    },
  });

  try {
    console.log("⏱️ Starting generation...");
    const startTime = Date.now();

    const result = await generator.generateAll();

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log("\n📊 Test Results:");
    console.log("================");

    if (result.success) {
      console.log("✅ Overall Status: SUCCESS");
      console.log(`⏱️ Total Duration: ${duration}ms`);

      // Print detailed results
      Object.entries(result.results).forEach(([key, value]) => {
        if (value && typeof value === "object" && "success" in value) {
          const status = value.success ? "✅" : "❌";
          const count = value.count || 0;
          const duration = value.duration || 0;
          console.log(`${status} ${key}: ${count} records (${duration}ms)`);
        }
      });
    } else {
      console.log("❌ Overall Status: FAILED");
      console.log("🚨 Errors:");
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    console.log("\n🎯 Test Summary:");
    console.log(`- Users: ${result.results.users?.count || 0}`);
    console.log(`- Categories: ${result.results.categories?.count || 0}`);
    console.log(`- Products: ${result.results.products?.count || 0}`);
    console.log(`- Reviews: ${result.results.reviews?.count || 0}`);
    console.log(`- Orders: ${result.results.orders?.count || 0}`);

    if (result.success) {
      console.log("\n🎉 All generators working correctly!");
      return true;
    } else {
      console.log("\n💥 Some generators failed. Check the errors above.");
      return false;
    }
  } catch (error) {
    console.error("\n💥 Test failed with exception:", error);
    return false;
  } finally {
    await generator.cleanup();
  }
}

async function main() {
  const success = await testGenerators();
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main();
}
