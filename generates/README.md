# NS Shop Data Generator

Hệ thống tạo dữ liệu mẫu hoàn chỉnh cho NS Shop sử dụng Faker.js và các thư viện chuyên dụng.

## 🚀 Tính năng

- **Tạo dữ liệu thực tế**: Sử dụng Faker.js để tạo dữ liệu Vietnamese realistic
- **Cấu trúc modular**: Mỗi entity có generator riêng biệt
- **CLI interface**: Giao diện command line linh hoạt
- **Configuration system**: <PERSON><PERSON> thể tùy chỉnh số lượng và loại dữ liệu
- **Validation**: Kiểm tra dữ liệu trước khi tạo
- **Transaction support**: Đảm bảo tính nhất quán dữ liệu
- **Verbose logging**: Theo dõi quá trình tạo dữ liệu

## 📁 Cấu trú<PERSON> thư mục

```
generates/
├── generators/           # Các generator classes
│   ├── base/            # Base generator class
│   ├── user.generator.ts
│   ├── product.generator.ts
│   ├── category.generator.ts
│   ├── order.generator.ts
│   └── review.generator.ts
├── templates/           # Templates cho dữ liệu
│   ├── products/
│   ├── users/
│   └── reviews/
├── data/               # Static data files
│   ├── categories.json
│   ├── product-names.json
│   └── vietnamese-names.json
├── utils/              # Utility functions
│   ├── slug.utils.ts
│   ├── image.utils.ts
│   └── validation.utils.ts
├── config/             # Configuration files
│   └── default.config.ts
├── cli/                # CLI interface
│   └── index.ts
├── index.ts            # Main orchestrator
├── run.ts              # Simple runner script
└── README.md           # Documentation
```

## 🛠️ Cài đặt

Các thư viện đã được cài đặt sẵn:

```bash
# Core libraries
@faker-js/faker
commander
chalk
inquirer
lodash
date-fns

# Types
@types/inquirer
@types/lodash
```

## 📖 Cách sử dụng

### 1. Sử dụng CLI (Khuyến nghị)

#### Tạo tất cả dữ liệu với cấu hình mặc định:
```bash
npm run generate
# hoặc
yarn generate
```

#### Chế độ interactive:
```bash
npm run generate:interactive
# hoặc
yarn generate:interactive
```

#### Tạo từng loại dữ liệu riêng biệt:
```bash
# Chỉ tạo users
npm run generate:users

# Chỉ tạo products
npm run generate:products

# Xem cấu hình mặc định
npm run generate:config
```

#### Với tùy chọn:
```bash
# Tạo với số lượng tùy chỉnh
npm run generate -- --users 50 --products 100 --reviews 200 --orders 30

# Với verbose logging
npm run generate -- --verbose

# Với seed cố định (để tạo dữ liệu giống nhau)
npm run generate -- --seed 12345
```

### 2. Sử dụng programmatically

```typescript
import { DataGenerator } from './generates';

const generator = new DataGenerator({
  users: {
    count: 20,
    includeAdmin: true,
    includeAddress: true,
  },
  products: {
    count: 50,
    salePercentage: 30,
    featuredPercentage: 20,
  },
  reviews: {
    count: 100,
    verifiedPercentage: 70,
  },
  orders: {
    count: 30,
  },
  global: {
    verbose: true,
    seed: 12345, // Optional: for reproducible results
  },
});

const result = await generator.generateAll();
console.log(result);
```

### 3. Sử dụng từng generator riêng biệt

```typescript
import { UserGenerator, ProductGenerator } from './generates';

// Tạo users
const userGenerator = new UserGenerator({
  count: 10,
  includeAdmin: true,
  verbose: true,
});

const userResult = await userGenerator.generate();
await userGenerator.cleanup();

// Tạo products
const productGenerator = new ProductGenerator({
  count: 20,
  salePercentage: 25,
  verbose: true,
});

const productResult = await productGenerator.generate();
await productGenerator.cleanup();
```

## ⚙️ Cấu hình

### Cấu hình mặc định

```typescript
{
  users: {
    count: 20,
    includeAdmin: true,
    includeAddress: true,
    includeAvatar: true,
  },
  categories: {
    count: 0, // Sử dụng categories mặc định
    useDefaultCategories: true,
    includeImages: false,
  },
  products: {
    count: 50,
    includeVariants: true,
    includeDimensions: false,
    salePercentage: 30,
    featuredPercentage: 20,
    minPrice: 50000,
    maxPrice: 2000000,
    minStock: 0,
    maxStock: 100,
  },
  reviews: {
    count: 100,
    verifiedPercentage: 70,
    includeHelpfulCount: true,
    ratingDistribution: {
      1: 5,   // 5%
      2: 10,  // 10%
      3: 15,  // 15%
      4: 35,  // 35%
      5: 35   // 35%
    },
  },
  orders: {
    count: 30,
    minItems: 1,
    maxItems: 5,
    statusDistribution: {
      PENDING: 10,
      CONFIRMED: 15,
      PROCESSING: 20,
      SHIPPED: 25,
      DELIVERED: 25,
      CANCELLED: 5,
    },
    paymentMethodDistribution: {
      COD: 60,
      BANK_TRANSFER: 25,
      CREDIT_CARD: 10,
      E_WALLET: 5,
    },
  },
  global: {
    locale: 'vi',
    verbose: true,
    seed: undefined, // Random seed
  },
}
```

## 📊 Dữ liệu được tạo

### Users (20 người dùng)
- Tên tiếng Việt thực tế
- Email unique
- Số điện thoại Việt Nam
- Địa chỉ Việt Nam chi tiết
- Avatar từ Faker
- 1 admin user (nếu enabled)

### Categories (Danh mục mặc định)
- Thời trang nữ (Áo nữ, Quần nữ, Váy đầm, Giày nữ)
- Thời trang nam (Áo nam, Quần nam, Giày nam)
- Phụ kiện (Túi xách, Trang sức, Mũ nón, Kính mắt)
- Thể thao (Áo thể thao, Quần thể thao, Giày thể thao)
- Trẻ em (Áo trẻ em, Quần trẻ em, Giày trẻ em)

### Products (50 sản phẩm)
- Tên sản phẩm tiếng Việt realistic
- Mô tả chi tiết
- Giá từ 50,000 - 2,000,000 VND
- 30% có giá sale
- 20% featured
- SKU tự động
- Stock ngẫu nhiên
- Hình ảnh từ Unsplash
- Variants (size, color)
- Tags phù hợp

### Reviews (100 đánh giá)
- Nội dung tiếng Việt thực tế
- Rating distribution realistic (nhiều 4-5 sao)
- 70% verified reviews
- Helpful count
- Tự động cập nhật avgRating cho products

### Orders (30 đơn hàng)
- 1-5 sản phẩm mỗi đơn
- Địa chỉ giao hàng Việt Nam
- Phương thức thanh toán realistic (60% COD)
- Trạng thái đơn hàng đa dạng
- Phí ship (miễn phí từ 500k)
- Discount ngẫu nhiên
- Tự động trừ stock khi đơn hàng confirmed/delivered

## 🔧 Scripts có sẵn

```bash
# Tạo dữ liệu mới (khuyến nghị)
npm run generate
npm run generate:interactive

# Tạo từng loại riêng biệt
npm run generate:users
npm run generate:products

# Xem cấu hình
npm run generate:config

# Setup database hoàn chỉnh (mới)
npm run db:setup-new

# Scripts cũ (vẫn hoạt động)
npm run db:seed
npm run db:generate-data
npm run db:full-setup
```

## 🧪 Testing

Để test generators:

```bash
# Chạy với seed cố định
npm run generate -- --seed 12345 --verbose

# Chạy với số lượng nhỏ để test
npm run generate -- --users 5 --products 10 --reviews 20 --orders 5
```

## 🚀 Production

Cho production, chỉ chạy:
```bash
npm run db:seed
```

Không chạy generators trên production vì chúng tạo dữ liệu test.

## 🔍 Troubleshooting

### Lỗi "No categories available"
- Chạy `npm run db:seed` trước để tạo categories
- Hoặc set `useDefaultCategories: true` trong config

### Lỗi "No users/products available"
- Đảm bảo tạo users trước khi tạo reviews/orders
- Đảm bảo tạo products trước khi tạo reviews/orders

### Lỗi duplicate data
- Generators tự động skip duplicate data
- Sử dụng `--seed` để tạo dữ liệu reproducible

### Performance issues
- Giảm số lượng trong config
- Tắt verbose logging với `--no-verbose`
- Chạy từng generator riêng biệt

## 📝 Customization

### Thêm tên sản phẩm mới
Chỉnh sửa `generates/data/product-names.json`

### Thêm template review mới
Chỉnh sửa `generates/templates/reviews/review-templates.json`

### Thêm tên Việt Nam mới
Chỉnh sửa `generates/data/vietnamese-names.json`

### Tạo generator mới
Extend từ `BaseGenerator` class trong `generates/generators/base/base.generator.ts`
