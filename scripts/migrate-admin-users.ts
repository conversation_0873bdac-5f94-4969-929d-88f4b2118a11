import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function migrateAdminUsers() {
  console.log('🔄 Starting admin user migration...');

  try {
    // Check if there are any admin users in the old system
    // Since the role column was already dropped, we'll create default admin users
    
    // Check if admin users already exist
    const existingAdmins = await prisma.adminUser.findMany();
    
    if (existingAdmins.length > 0) {
      console.log(`✅ Found ${existingAdmins.length} existing admin users. Migration already completed.`);
      return;
    }

    // Create default admin user
    const adminPassword = await bcrypt.hash('admin123', 12);
    const admin = await prisma.adminUser.create({
      data: {
        email: '<EMAIL>',
        name: 'NS Shop Admin',
        password: adminPassword,
        role: 'ADMIN',
        isActive: true,
      },
    });

    // Create default moderator user
    const moderatorPassword = await bcrypt.hash('moderator123', 12);
    const moderator = await prisma.adminUser.create({
      data: {
        email: '<EMAIL>',
        name: 'NS Shop Moderator',
        password: moderatorPassword,
        role: 'MODERATOR',
        isActive: true,
        permissions: {
          manage_products: true,
          manage_orders: true,
          manage_categories: true,
          view_analytics: true,
          manage_users: true,
        },
        createdBy: admin.id,
      },
    });

    console.log('✅ Created default admin users:');
    console.log(`   - Admin: ${admin.email}`);
    console.log(`   - Moderator: ${moderator.email}`);
    console.log('🔐 Default passwords: admin123, moderator123');
    console.log('⚠️  Please change these passwords after first login!');

  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateAdminUsers()
  .then(() => {
    console.log('✅ Admin user migration completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  });
