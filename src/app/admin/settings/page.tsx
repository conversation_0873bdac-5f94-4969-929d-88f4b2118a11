"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  Settings,
  Save,
  Globe,
  Mail,
  Phone,
  CreditCard,
  Truck,
  Shield,
  Bell,
  RefreshCw,
  RotateCcw,
} from "lucide-react";
import { toast } from "sonner";

interface SiteSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  logo: string;
  favicon: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  socialMedia: {
    facebook: string;
    instagram: string;
    twitter: string;
  };
  paymentMethods: {
    cod: boolean;
    bankTransfer: boolean;
    creditCard: boolean;
  };
  shippingSettings: {
    freeShippingThreshold: number;
    shippingFee: number;
    estimatedDelivery: string;
  };
  emailSettings: {
    smtpHost: string;
    smtpPort: number;
    smtpUser: string;
    smtpPassword: string;
    fromEmail: string;
    fromName: string;
  };
  notifications: {
    orderNotifications: boolean;
    stockAlerts: boolean;
    customerNotifications: boolean;
  };
}

export default function AdminSettingsPage() {
  const [settings, setSettings] = useState<SiteSettings>({
    siteName: "",
    siteDescription: "",
    siteUrl: "",
    logo: "",
    favicon: "",
    contactEmail: "",
    contactPhone: "",
    address: "",
    socialMedia: {
      facebook: "",
      instagram: "",
      twitter: "",
    },
    paymentMethods: {
      cod: true,
      bankTransfer: true,
      creditCard: false,
    },
    shippingSettings: {
      freeShippingThreshold: 500000,
      shippingFee: 30000,
      estimatedDelivery: "2-3 ngày",
    },
    emailSettings: {
      smtpHost: "",
      smtpPort: 587,
      smtpUser: "",
      smtpPassword: "",
      fromEmail: "",
      fromName: "",
    },
    notifications: {
      orderNotifications: true,
      stockAlerts: true,
      customerNotifications: true,
    },
  });

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("general");

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    setInitialLoading(true);
    try {
      const response = await fetch("/api/admin/settings");
      const data = await response.json();

      if (response.ok) {
        setSettings((prev) => ({ ...prev, ...data }));
      } else {
        toast.error("Có lỗi xảy ra khi tải cài đặt");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải cài đặt");
    } finally {
      setInitialLoading(false);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ settings }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Cài đặt đã được lưu thành công");
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi lưu cài đặt");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi lưu cài đặt");
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    if (
      !confirm(
        "Bạn có chắc chắn muốn khôi phục cài đặt mặc định? Tất cả thay đổi sẽ bị mất."
      )
    ) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/settings?action=reset", {
        method: "POST",
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Đã khôi phục cài đặt mặc định");
        await fetchSettings();
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi khôi phục cài đặt");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi khôi phục cài đặt");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (section: string, field: string, value: any) => {
    setSettings((prev) => {
      const currentSection = prev[section as keyof SiteSettings];
      return {
        ...prev,
        [section]: {
          ...(typeof currentSection === "object" && currentSection !== null
            ? currentSection
            : {}),
          [field]: value,
        },
      };
    });
  };

  const handleDirectChange = (field: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const tabs = [
    { id: "general", label: "Tổng quan", icon: Settings },
    { id: "contact", label: "Liên hệ", icon: Phone },
    { id: "payment", label: "Thanh toán", icon: CreditCard },
    { id: "shipping", label: "Vận chuyển", icon: Truck },
    { id: "email", label: "Email", icon: Mail },
    { id: "notifications", label: "Thông báo", icon: Bell },
    { id: "seo", label: "SEO", icon: Globe },
    { id: "security", label: "Bảo mật", icon: Shield },
  ];

  if (initialLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Cài đặt hệ thống</h1>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-0">
                <div className="space-y-1">
                  {Array.from({ length: 8 }, (_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-12 bg-gray-200 rounded mx-4 my-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="lg:col-span-3">
            <Card>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  {Array.from({ length: 6 }, (_, i) => (
                    <div key={i} className="h-4 bg-gray-200 rounded" />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case "general":
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Tên website
                </label>
                <input
                  type="text"
                  value={settings.siteName}
                  onChange={(e) =>
                    handleDirectChange("siteName", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  URL website
                </label>
                <input
                  type="url"
                  value={settings.siteUrl}
                  onChange={(e) =>
                    handleDirectChange("siteUrl", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Mô tả website
              </label>
              <textarea
                value={settings.siteDescription}
                onChange={(e) =>
                  handleDirectChange("siteDescription", e.target.value)
                }
                rows={3}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
          </div>
        );

      case "contact":
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Email liên hệ
                </label>
                <input
                  type="email"
                  value={settings.contactEmail}
                  onChange={(e) =>
                    handleDirectChange("contactEmail", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  Số điện thoại
                </label>
                <input
                  type="tel"
                  value={settings.contactPhone}
                  onChange={(e) =>
                    handleDirectChange("contactPhone", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Địa chỉ</label>
              <textarea
                value={settings.address}
                onChange={(e) => handleDirectChange("address", e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
          </div>
        );

      case "payment":
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">
                Phương thức thanh toán
              </h3>
              <div className="space-y-4">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.paymentMethods.cod}
                    onChange={(e) =>
                      handleInputChange(
                        "paymentMethods",
                        "cod",
                        e.target.checked
                      )
                    }
                    className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
                  />
                  <span>Thanh toán khi nhận hàng (COD)</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.paymentMethods.bankTransfer}
                    onChange={(e) =>
                      handleInputChange(
                        "paymentMethods",
                        "bankTransfer",
                        e.target.checked
                      )
                    }
                    className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
                  />
                  <span>Chuyển khoản ngân hàng</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.paymentMethods.creditCard}
                    onChange={(e) =>
                      handleInputChange(
                        "paymentMethods",
                        "creditCard",
                        e.target.checked
                      )
                    }
                    className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
                  />
                  <span>Thẻ tín dụng</span>
                </label>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Tab content not implemented yet</div>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Cài đặt hệ thống</h1>
          <p className="text-muted-foreground">
            Quản lý cài đặt và cấu hình website
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchSettings} variant="outline" disabled={loading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Làm mới
          </Button>
          <Button onClick={handleReset} variant="outline" disabled={loading}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Khôi phục mặc định
          </Button>
          <Button
            onClick={handleSave}
            disabled={loading}
            className="bg-pink-600 hover:bg-pink-700"
          >
            <Save className="h-4 w-4 mr-2" />
            {loading ? "Đang lưu..." : "Lưu cài đặt"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                        activeTab === tab.id
                          ? "bg-pink-50 text-pink-600 border-r-2 border-pink-600"
                          : "text-gray-600"
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>
                {tabs.find((tab) => tab.id === activeTab)?.label}
              </CardTitle>
            </CardHeader>
            <CardContent>{renderTabContent()}</CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
