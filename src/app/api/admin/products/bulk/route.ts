import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../auth/[...nextauth]/route";
import { z } from "zod";

const bulkUpdateSchema = z.object({
  productIds: z.array(z.string()).min(1, "Phải chọn ít nhất 1 sản phẩm"),
  action: z.enum([
    "delete",
    "activate",
    "deactivate",
    "feature",
    "unfeature",
    "update_category",
  ]),
  data: z
    .object({
      categoryId: z.string().optional(),
    })
    .optional(),
});

// POST /api/admin/products/bulk - Bulk operations cho products
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "<PERSON>hông có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { productIds, action, data } = bulkUpdateSchema.parse(body);

    let result;
    let message = "";

    switch (action) {
      case "delete":
        // Soft delete by setting status to INACTIVE
        result = await prisma.product.updateMany({
          where: {
            id: { in: productIds },
          },
          data: {
            status: "INACTIVE",
          },
        });
        message = `Đã xóa ${result.count} sản phẩm`;
        break;

      case "activate":
        result = await prisma.product.updateMany({
          where: {
            id: { in: productIds },
          },
          data: {
            status: "ACTIVE",
          },
        });
        message = `Đã kích hoạt ${result.count} sản phẩm`;
        break;

      case "deactivate":
        result = await prisma.product.updateMany({
          where: {
            id: { in: productIds },
          },
          data: {
            status: "INACTIVE",
          },
        });
        message = `Đã vô hiệu hóa ${result.count} sản phẩm`;
        break;

      case "feature":
        result = await prisma.product.updateMany({
          where: {
            id: { in: productIds },
          },
          data: {
            featured: true,
          },
        });
        message = `Đã đặt ${result.count} sản phẩm làm nổi bật`;
        break;

      case "unfeature":
        result = await prisma.product.updateMany({
          where: {
            id: { in: productIds },
          },
          data: {
            featured: false,
          },
        });
        message = `Đã bỏ nổi bật ${result.count} sản phẩm`;
        break;

      case "update_category": {
        if (!data?.categoryId) {
          return NextResponse.json(
            { error: "Phải chọn danh mục" },
            { status: 400 }
          );
        }

        // Check if category exists
        const category = await prisma.category.findUnique({
          where: { id: data.categoryId },
        });

        if (!category) {
          return NextResponse.json(
            { error: "Danh mục không tồn tại" },
            { status: 400 }
          );
        }

        result = await prisma.product.updateMany({
          where: {
            id: { in: productIds },
          },
          data: {
            categoryId: data.categoryId,
          },
        });
        message = `Đã cập nhật danh mục cho ${result.count} sản phẩm`;
        break;
      }

      default:
        return NextResponse.json(
          { error: "Hành động không hợp lệ" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      message,
      count: result.count,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Bulk products operation error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi thực hiện thao tác" },
      { status: 500 }
    );
  }
}
