import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { adminAuthOptions } from '../../auth/[...nextauth]/route';
import bcrypt from 'bcryptjs';
import { z } from 'zod';

const createAdminSchema = z.object({
  email: z.string().email('Email không hợp lệ'),
  name: z.string().min(2, 'Tên phải có ít nhất 2 ký tự'),
  password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
  role: z.enum(['ADMIN', 'MODERATOR']),
  phone: z.string().optional(),
  department: z.string().optional(),
  permissions: z.record(z.boolean()).optional(),
});

// GET /api/admin/admins - Get list of admin users
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== 'admin') {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 403 }
      );
    }

    // Only ADMIN can view all admin users
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Chỉ Admin mới có quyền xem danh sách quản trị viên' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const role = searchParams.get('role');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (role) {
      where.role = role;
    }

    // Get admin users with pagination
    const [adminUsers, total] = await Promise.all([
      prisma.adminUser.findMany({
        where,
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          role: true,
          avatar: true,
          isActive: true,
          department: true,
          lastLoginAt: true,
          createdAt: true,
          createdByAdmin: {
            select: {
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.adminUser.count({ where }),
    ]);

    return NextResponse.json({
      adminUsers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Get admin users error:', error);
    return NextResponse.json(
      { error: 'Có lỗi xảy ra khi lấy danh sách quản trị viên' },
      { status: 500 }
    );
  }
}

// POST /api/admin/admins - Create new admin user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== 'admin') {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 403 }
      );
    }

    // Only ADMIN can create new admin users
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Chỉ Admin mới có quyền tạo tài khoản quản trị viên' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const data = createAdminSchema.parse(body);

    // Check if email already exists
    const existingAdmin = await prisma.adminUser.findUnique({
      where: { email: data.email },
    });

    if (existingAdmin) {
      return NextResponse.json(
        { error: 'Email đã được sử dụng' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(data.password, 12);

    // Create admin user
    const adminUser = await prisma.adminUser.create({
      data: {
        name: data.name,
        email: data.email,
        password: hashedPassword,
        role: data.role,
        phone: data.phone,
        department: data.department,
        permissions: data.permissions,
        createdBy: session.user.id,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
        department: true,
        isActive: true,
        createdAt: true,
      },
    });

    return NextResponse.json(
      {
        message: 'Tạo tài khoản quản trị viên thành công',
        adminUser,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Dữ liệu không hợp lệ', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Create admin user error:', error);
    return NextResponse.json(
      { error: 'Có lỗi xảy ra khi tạo tài khoản quản trị viên' },
      { status: 500 }
    );
  }
}
