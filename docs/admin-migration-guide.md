# Admin System Migration Guide

## Overview

This guide walks you through migrating from the old admin system (where admin users were stored in the `users` table) to the new separate admin system.

## Pre-Migration Checklist

Before starting the migration, ensure you have:

- [ ] Database backup
- [ ] Development environment for testing
- [ ] Access to the database
- [ ] Node.js and npm/yarn installed
- [ ] Understanding of the current admin users in your system

## Migration Steps

### Step 1: Backup Your Database

```bash
# PostgreSQL backup
pg_dump your_database_name > backup_before_admin_migration.sql

# Or using environment variable
pg_dump $DATABASE_URL > backup_before_admin_migration.sql
```

### Step 2: Update Dependencies

Ensure you have the latest dependencies:

```bash
npm install
# or
yarn install
```

### Step 3: Run Database Migration

The Prisma migration will create the new `AdminUser` table and update the schema:

```bash
npx prisma migrate deploy
```

This migration will:
- Create the `admin_users` table
- Add the `AdminRole` enum
- Remove the `role` field from the `users` table
- Add foreign key constraints for admin hierarchy

### Step 4: Migrate Existing Admin Data

Run the admin user migration script:

```bash
npx tsx scripts/migrate-admin-users.ts
```

This script will:
- Create default admin and moderator accounts
- Set up proper permissions for the moderator account
- Establish the admin hierarchy

### Step 5: Update Environment Variables

No new environment variables are required, but ensure your existing ones are correct:

```env
DATABASE_URL="your_database_url"
NEXTAUTH_SECRET="your_nextauth_secret"
NEXTAUTH_URL="your_app_url"
```

### Step 6: Test the Migration

1. **Test Admin Login**:
   - Go to `/admin/auth/signin`
   - Login with `<EMAIL>` / `admin123`
   - Verify you can access the admin dashboard

2. **Test Moderator Login**:
   - Login with `<EMAIL>` / `moderator123`
   - Verify limited access (no admin management, no settings)

3. **Test Regular User Login**:
   - Go to `/auth/signin`
   - Login with a regular user account
   - Verify normal user functionality

### Step 7: Update Admin Passwords

⚠️ **Critical**: Change the default admin passwords immediately:

1. Login as admin
2. Go to `/admin/admins`
3. Edit the admin and moderator accounts
4. Set strong, unique passwords

## Manual Data Migration (If Needed)

If you had existing admin users that need to be migrated manually:

### Identify Existing Admin Users

```sql
-- Before migration, identify admin users
SELECT id, name, email, role FROM users WHERE role = 'ADMIN';
```

### Create Admin Users Manually

```typescript
// Example script to create admin users
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createAdminFromOldUser(oldUser: any) {
  const hashedPassword = await bcrypt.hash('temporary_password', 12);
  
  await prisma.adminUser.create({
    data: {
      email: oldUser.email,
      name: oldUser.name,
      password: hashedPassword,
      role: 'ADMIN', // or 'MODERATOR' based on your needs
      isActive: true,
      // Add other fields as needed
    },
  });
}
```

## Verification Steps

### 1. Database Verification

Check that the migration completed successfully:

```sql
-- Verify admin_users table exists
SELECT COUNT(*) FROM admin_users;

-- Verify users table no longer has role column
\d users;

-- Check default admin accounts
SELECT email, role, is_active FROM admin_users;
```

### 2. Authentication Verification

Test both authentication systems:

- Regular users: `/auth/signin`
- Admin users: `/admin/auth/signin`

### 3. Permission Verification

Test role-based access:

- Admin can access `/admin/admins`
- Moderator cannot access `/admin/admins`
- Moderator can access `/admin/products`

## Rollback Plan

If you need to rollback the migration:

### Step 1: Restore Database Backup

```bash
# Stop the application
# Restore from backup
psql your_database_name < backup_before_admin_migration.sql
```

### Step 2: Revert Code Changes

```bash
git checkout previous_commit_hash
```

### Step 3: Run Previous Migration

```bash
npx prisma migrate deploy
```

## Common Issues and Solutions

### Issue 1: Migration Fails

**Problem**: Prisma migration fails due to existing data

**Solution**:
1. Check for data conflicts
2. Manually resolve conflicts
3. Re-run migration

### Issue 2: Admin Login Not Working

**Problem**: Cannot login to admin panel

**Solutions**:
1. Verify admin accounts were created: `SELECT * FROM admin_users;`
2. Check password hashing
3. Verify NextAuth configuration

### Issue 3: Permission Errors

**Problem**: Moderators can access admin-only features

**Solutions**:
1. Check middleware configuration
2. Verify role checking in API endpoints
3. Clear browser cache and cookies

### Issue 4: Regular Users Cannot Login

**Problem**: Regular user authentication broken

**Solutions**:
1. Verify user table structure
2. Check regular auth configuration
3. Ensure user auth endpoints are unchanged

## Post-Migration Tasks

### 1. Update Documentation

- Update internal documentation
- Train admin users on new login process
- Document new permission system

### 2. Security Review

- Change all default passwords
- Review admin permissions
- Set up monitoring for admin actions

### 3. Monitoring Setup

- Monitor admin login attempts
- Track admin user creation/deletion
- Set up alerts for suspicious admin activity

### 4. Backup Strategy

- Update backup procedures to include admin_users table
- Test restore procedures
- Document new database schema

## Testing Checklist

After migration, test the following:

- [ ] Admin login at `/admin/auth/signin`
- [ ] Moderator login with limited permissions
- [ ] Regular user login at `/auth/signin`
- [ ] Admin user management interface
- [ ] Role-based access control
- [ ] API endpoint authentication
- [ ] Middleware route protection
- [ ] Session management
- [ ] Password changes
- [ ] Account deactivation/activation

## Support

If you encounter issues during migration:

1. Check the troubleshooting section in `docs/admin-system.md`
2. Review the test files for expected behavior
3. Check the database logs for errors
4. Verify all environment variables are set correctly

## Next Steps

After successful migration:

1. **Security Hardening**: Implement additional security measures
2. **User Training**: Train admin users on the new system
3. **Monitoring**: Set up comprehensive admin activity monitoring
4. **Documentation**: Keep documentation updated with any customizations
5. **Regular Audits**: Schedule regular reviews of admin accounts and permissions
